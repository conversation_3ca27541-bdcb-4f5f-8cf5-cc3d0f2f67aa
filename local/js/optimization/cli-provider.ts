import {
  _internalGetGlobalState,
  Dataset,
  EvalResultWithSummary,
  EvalScorer,
  Experiment,
  initDataset,
  initExperiment,
  initFunction,
  newId,
  runEvaluator,
  Span,
} from "braintrust";
import { MetaEval } from "./evals/meta-eval";
import {
  applyEdits,
  ContinueExecutionToolParameters,
  EditDataToolParameters,
  EditTaskToolParameters,
  EditTaskToolResult,
  GetSummaryToolResult,
  GetResultsToolParameters,
  GetResultsToolResult,
  isInternalMetric,
  RerunTaskToolParameters,
  RerunTaskToolResult,
  roundNumber,
  ScoreSummary,
  BTQLQueryToolResult,
  BTQLQueryToolParameters,
  InferSchemaToolParameters,
  InferSchemaToolResult,
  inferSchemaToolResultSchema,
  btqlQueryToolResultSchema,
} from "./tools";
import { ToolManager } from "./tools/registry";
import { Spinner } from "./cli";
import {
  GetAvailableScorersResult,
  GetAvailableScorersParams,
} from "./tools/get-scorers";
import { Evaluators, Scorer } from "autoevals";
import {
  EditScorersParams,
  EditScorersToolResult,
  getGlobalScorers,
} from "./tools/edit-scorers";
import {
  CreateLLMScorerParams,
  CreateCodeScorerParams,
} from "./tools/create-scorer";
import slugifyLib from "slugify";
import { v4 as uuidv4 } from "uuid";
import { PageKey } from "./llm/system-prompt";
import {
  DocsToolParams,
  DocsToolResult,
  docsToolResultSchema,
} from "./tools/docs";
import { PromptData } from "@braintrust/typespecs";

export async function makeTools({
  def,
  projectName,
  queryObjectType,
  queryObjectId,
  logProjectName,
  spinner,
  printExperimentResult,
  deterministic,
  model,
  applicationContext = "playground",
  preLoadedExperiment,
}: {
  def?: MetaEval;
  projectName: string;
  // If provided, will query the logs from this object. This is as opposed to logProjectName, which is where
  // we want to store the logs generated by Loop.
  queryObjectType?: string;
  queryObjectId?: string;
  logProjectName: string;
  spinner?: Spinner;
  printExperimentResult?: (result: string) => void;
  deterministic?: boolean;
  model: string;
  //This exists to be able to simulate running Loop in different parts of the application.
  //Based on the page, Loop has access to different tools or same tools but with different parameters.
  //If left out, it defaults to playground which aligns with the behavior of the CLI as it was originally written.
  applicationContext?: PageKey;
  preLoadedExperiment?: Experiment;
}): Promise<ToolManager> {
  const tools = new ToolManager(model.includes("gemini"));
  let lastSummary: EvalResultWithSummary<unknown, unknown, unknown> | null =
    null;
  const task = def?.task.copy();
  // If we have an initial experiment, load its data into lastSummary. This is to emulate experiment page.
  if (preLoadedExperiment) {
    try {
      const summary = await preLoadedExperiment.summarize();
      const results = [];

      // Fetch the experiment results
      for await (const record of preLoadedExperiment.fetch()) {
        if (record.root_span_id !== record.span_id) {
          continue; // Skip non-root spans
        }

        results.push({
          id: record.id,
          input: record.input,
          output: record.output,
          expected: record.expected,
          metadata: record.metadata,
          scores: record.scores || {},
          metrics: record.metrics || {},
          error: undefined,
          origin: {
            object_type: "experiment" as const,
            object_id: await preLoadedExperiment.id,
            id: record.id,
          },
        });
      }

      lastSummary = new EvalResultWithSummary(summary, results);
    } catch (error) {
      console.warn("Failed to load initial experiment data:", error);
    }
  }

  //Adding uuid to the dataset name to avoid things being added to the same dataset when running multiple evals in parallel.
  const datasetName = `${def?.dataset ?? "dataset"}-${def?.name ?? "name"}-${new Date().toISOString()}-${uuidv4()}`;
  const dataset = initDataset({
    project: logProjectName,
    dataset: datasetName,
  });

  const logProjectId = (await dataset.project).id;

  if (def) {
    const initialData = initDataset({
      project: projectName,
      dataset: def.dataset,
    });

    for await (const datum of initialData) {
      dataset.insert({
        id: datum.id,
        tags: datum.tags,
        input: datum.input,
        expected: datum.expected,
        metadata: datum.metadata ?? undefined,
      });
    }
    await dataset.flush();
  }

  const customScorers: (GetAvailableScorersResult & {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    method: Scorer<any, any>;
  })[] = [];

  tools.updateImplementations({
    get_summary:
      def && task
        ? async (): Promise<GetSummaryToolResult[]> => {
            if (applicationContext === "experiments") {
              return [
                {
                  taskName: def.name,
                  definition: {},
                  index: 0,
                  ...(lastSummary
                    ? computeLastScoresAndMetrics(lastSummary)
                    : { scores: {}, metrics: {} }),
                },
              ];
            }
            return [
              {
                taskName: "test",
                definition: task.serialize(),
                index: 0,
                ...(lastSummary
                  ? computeLastScoresAndMetrics(lastSummary)
                  : { scores: {}, metrics: {} }),
              },
            ];
          }
        : undefined,
    get_results: def
      ? async (
          params: GetResultsToolParameters,
        ): Promise<GetResultsToolResult[]> => {
          if (applicationContext === "playground") {
            return getPlaygroundResults(
              params,
              lastSummary,
              deterministic ?? false,
            );
          } else if (applicationContext === "dataset") {
            return getDatasetResults(params, dataset, deterministic ?? false);
          } else {
            throw new Error(
              "Cannot get results for page other than playground or dataset",
            );
          }
        }
      : undefined,
    edit_task:
      def && task
        ? async (
            params: EditTaskToolParameters,
            span: Span,
          ): Promise<EditTaskToolResult> => {
            const definitionData = task.serialize();
            const newDefinitionData = applyEdits(
              definitionData,
              params.edits,
              span,
            );
            task.deserialize(newDefinitionData);
            return { ok: true };
          }
        : undefined,
    run_task:
      def && task
        ? async (
            params: RerunTaskToolParameters,
            span: Span,
          ): Promise<RerunTaskToolResult> => {
            if (params.index !== 0) {
              throw new Error("Cannot rerun task with index other than 0");
            }

            // This is important because it'll get us _xact_id for the dataset rows
            dataset.clearCache();

            let expInc = 0;
            let expTotal = 0;

            lastSummary = await runEvaluator(
              initExperiment(logProjectName, {
                experiment: def.name,
                description: def.description,
                dataset,
              }),
              {
                projectName,
                evalName: def.name,
                data: dataset,
                task: async (input, hook) => {
                  return await task.execute(input, hook);
                },
                scores: def.scores,
              },
              {
                start: (name, total) => {
                  expTotal = total;
                  spinner?.start(`Running ${name}... (0/${total})`);
                },
                stop: () => {},
                increment: (name) => {
                  expInc += 1;
                  if (spinner) {
                    spinner.text = `Running ${name}... (${expInc}/${expTotal})`;
                  }
                },
              },
              [],
              undefined,
              undefined,
            );

            if (spinner) {
              spinner.text = "";
              spinner.succeed("Done!");
            }

            if (lastSummary.summary.experimentUrl && printExperimentResult) {
              span.log({
                metadata: {
                  experiment_url: lastSummary.summary.experimentUrl,
                },
              });
              printExperimentResult(
                `View experiment at:\n${lastSummary.summary.experimentUrl}`,
              );
            }
            return {
              summary: {
                taskName: def.name,
                index: 0,
                ...computeLastScoresAndMetrics(lastSummary),
              },
            };
          }
        : undefined,
    edit_data: async (params: EditDataToolParameters) => {
      for (const edit of params.edits) {
        if (!edit.id) {
          const metadata = {
            ...(edit.metadata ?? {}),
            ...(applicationContext === "dataset" ? { synthetic: true } : {}),
          };

          const data = {
            id: newId(),
            tags: undefined,
            input: edit.input,
            expected: edit.expected,
            metadata: metadata ?? undefined,
          };
          dataset.insert(data);
        } else {
          if (edit.delete) {
            dataset.delete(edit.id);
          } else {
            const metadata = {
              ...(edit.metadata ?? {}),
              ...(applicationContext === "dataset" ? { synthetic: true } : {}),
            };

            const data = {
              id: edit.id,
              ...(edit.input !== undefined ? { input: edit.input } : {}),
              ...(edit.expected !== undefined
                ? { expected: edit.expected }
                : {}),
              metadata: metadata ?? undefined,
            };
            dataset.update(data);
          }
        }
      }
      await dataset.flush();

      // Clear the dataset cache to ensure subsequent reads get the updated data
      dataset.clearCache();

      return { ok: true };
    },
    get_available_scorers: async (
      params?: GetAvailableScorersParams,
    ): Promise<GetAvailableScorersResult[]> => {
      const globalScorers = getGlobalScorers();

      // If a specific id is requested, look for it
      if (params?.id) {
        const globalScorer = globalScorers.find((s) => s.id === params.id);
        if (globalScorer) {
          return [globalScorer];
        }

        const customScorer = customScorers.find((s) => s.id === params.id);
        if (customScorer) {
          return [customScorer];
        }

        return [];
      }

      return [...globalScorers, ...customScorers];
    },
    edit_scorers: def
      ? async (params: EditScorersParams): Promise<EditScorersToolResult> => {
          for (const scorerParam of params.scorers) {
            let scorer: // eslint-disable-next-line @typescript-eslint/no-explicit-any
            { id: string; method: EvalScorer<any, any, any> } | undefined =
              undefined;
            const globalScorer = Evaluators.flatMap(
              (category) => category.methods,
            ).find((method) => method.method.name === scorerParam.id);
            if (globalScorer) {
              scorer = {
                id: globalScorer.method.name,
                method: globalScorer.method,
              };
            } else {
              for (const cs of customScorers) {
                if (cs.id === scorerParam.id) {
                  scorer = { id: cs.id, method: cs.method };
                  break;
                }
              }
            }
            if (!scorer) {
              throw new Error(`Scorer not found: ${scorerParam.id}`);
            }

            if (scorerParam.enabled) {
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
              if (!def.scores.some((s) => (s as unknown) === scorer)) {
                def.scores.push(scorer.method);
              }
            } else {
              def.scores = def.scores.filter(
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                (s) => (s as unknown) !== scorer.method,
              );
            }
          }
          return { ok: true };
        }
      : undefined,
    create_llm_scorer: def
      ? async (params: CreateLLMScorerParams) => {
          const apiConn = _internalGetGlobalState().apiConn();
          const slug =
            slugifyLib(params.name, { lower: true, strict: true }) +
            "-" +
            uuidv4().slice(-4);

          const prompt: PromptData = {
            prompt: {
              type: "chat",
              messages: [
                {
                  role: "system",
                  content: params.prompt,
                },
              ],
            },
            options: {
              model: params.model,
              params: params.params,
            },
            parser: {
              type: "llm_classifier",
              use_cot: params.use_cot,
              choice_scores: params.choice_scores,
            },
          };

          const result = await apiConn.post("/v1/function", {
            project_id: logProjectId,
            function_data: {
              type: "prompt",
            },
            prompt_data: prompt,
            name: params.name,
            slug,
            function_type: "scorer",
          });
          const { id } = await result.json();

          const method = initFunction({
            projectName: logProjectName,
            slug,
          });

          def.scores.push(method);

          customScorers.push({
            id,
            name: params.name,
            description: `LLM Scorer (${params.model})`,
            prompt,
            slug,
            type: "llm",
            method,
          });

          return { id };
        }
      : undefined,
    create_code_scorer: def
      ? async (params: CreateCodeScorerParams) => {
          const apiConn = _internalGetGlobalState().apiConn();
          const slug =
            slugifyLib(params.name, { lower: true, strict: true }) +
            "-" +
            uuidv4().slice(-4);

          const result = await apiConn.post("/v1/function", {
            project_id: logProjectId,
            function_data: {
              type: "code",
              data: {
                type: "inline",
                runtime_context:
                  params.runtime === "typescript"
                    ? { runtime: "node", version: "20" }
                    : {
                        runtime: "python",
                        version: "3.11",
                      },
                code: params.code,
              },
            },
            name: params.name,
            slug,
            function_type: "scorer",
          });

          const { id } = await result.json();

          const method = initFunction({
            projectName: logProjectName,
            slug,
          });

          def.scores.push(method);
          customScorers.push({
            id,
            name: params.name,
            description: `Code Scorer (${params.runtime})`,
            code: params.code,
            runtime: params.runtime,
            slug,
            type: "code",
            method,
          });

          return { id };
        }
      : undefined,
    infer_schema:
      queryObjectType && queryObjectId
        ? async (
            params: InferSchemaToolParameters,
          ): Promise<InferSchemaToolResult> => {
            const query = {
              from: {
                op: "function",
                name: {
                  op: "ident",
                  name: [queryObjectType],
                },
                args: [
                  {
                    op: "literal",
                    value: queryObjectId,
                  },
                ],
              },
              infer: [
                {
                  op: "star",
                },
              ],
              limit: 10000,
            };

            const result = await _internalGetGlobalState()
              .apiConn()
              .post("/btql", {
                query,
              });
            return inferSchemaToolResultSchema
              .strip()
              .parse(await result.json());
          }
        : undefined,
    btql_query:
      queryObjectType && queryObjectId
        ? async (
            params: BTQLQueryToolParameters,
          ): Promise<BTQLQueryToolResult> => {
            const query = `from: ${queryObjectType}('${queryObjectId}') ${params.shape ?? ""} | ${params.query}`;
            const result = await _internalGetGlobalState()
              .apiConn()
              .post("/btql", {
                query,
              });
            const data = btqlQueryToolResultSchema
              .omit({ rowCount: true })
              .parse(await result.json());
            return {
              rowCount: data.data.length,
              ...data,
            };
          }
        : undefined,
    search_docs: async (params: DocsToolParams): Promise<DocsToolResult> => {
      const payload = { query: params.query, topK: params.topK };
      const result = await _internalGetGlobalState()
        .appConn()
        .post("/api/docs/search", payload);
      const data = docsToolResultSchema.parse(await result.json());
      return data;
    },
    continue_execution: async (_params: ContinueExecutionToolParameters) => {
      return { allowed: false };
    },
  });
  return tools;
}

function computeLastScoresAndMetrics(
  summary: EvalResultWithSummary<unknown, unknown, unknown>,
): {
  scores: Record<string, ScoreSummary>;
  metrics: Record<string, ScoreSummary>;
} {
  const scores: Record<string, ScoreSummary> = {};
  const metrics: Record<string, ScoreSummary> = {};

  for (const [name, score] of Object.entries(summary.summary.scores)) {
    scores[name] = {
      avg: roundNumber(score.score),
      min: undefined,
      max: undefined,
    };
  }
  for (const [name, metric] of Object.entries(summary.summary.metrics ?? {})) {
    if (isInternalMetric(name)) {
      continue;
    }
    metrics[name] = {
      avg: roundNumber(metric.metric),
      min: undefined,
      max: undefined,
    };
  }

  for (const result of summary.results) {
    if (result.scores) {
      for (const [key, value] of Object.entries(result.scores)) {
        const v = roundNumber(value);
        if (v !== undefined && scores[key] !== undefined) {
          scores[key].max = Math.max(scores[key].max ?? 0, v);
          scores[key].min = Math.min(scores[key].min ?? 0, v);
        }
      }
    }
  }

  return { scores, metrics };
}

function getUniqueRandomNumbers(setSize: number, range: number) {
  if (setSize >= range) {
    return Array.from({ length: range }, (_, i) => i);
  }
  const nums = Array.from({ length: range }, (_, i) => i);
  for (let i = nums.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [nums[i], nums[j]] = [nums[j], nums[i]]; // Fisher–Yates shuffle
  }
  return nums.slice(0, setSize);
}

async function getDatasetResults(
  params: GetResultsToolParameters,
  dataset: Dataset,
  deterministic: boolean,
) {
  if (params.numSamples === 0) {
    return [];
  }

  const results: GetResultsToolResult[] = [];
  let count = 0;

  for await (const row of dataset.fetch()) {
    const result = {
      id: row.id,
      input: row.input,
      output: undefined,
      metadata: row.metadata,
      expected: row.expected,
      scores: {},
      metrics: {},
    };

    if (deterministic) {
      // If deterministic, take first N rows
      if (results.length < params.numSamples) {
        results.push(result);
      } else {
        break;
      }
    } else {
      if (results.length < params.numSamples) {
        results.push(result);
      } else {
        // Randomly decide whether to include this row
        const j = Math.floor(Math.random() * (count + 1));
        if (j < params.numSamples) {
          results[j] = result;
        }
      }
    }
    count++;
  }
  return results;
}

function getPlaygroundResults(
  params: GetResultsToolParameters,
  lastSummary: EvalResultWithSummary<unknown, unknown, unknown> | null,
  deterministic: boolean,
) {
  if (params.index !== 0) {
    throw new Error("Cannot get results for task with index other than 0");
  } else if (!lastSummary) {
    return [];
  }

  const lastResults = lastSummary.results;

  // Generate params.numSamples random indices between 0 and data.length - 1
  const indices = deterministic
    ? Array.from(
        { length: Math.min(params.numSamples, lastResults.length) },
        (_, i) => i,
      )
    : getUniqueRandomNumbers(params.numSamples, lastResults.length);
  const indexArr = Array.from(indices);
  const results: GetResultsToolResult[] = [];
  for (const index of indexArr) {
    const output = lastResults[index];
    if (!output.origin) {
      throw new Error("Origin not found (this is likely a bug)");
    }
    const result: GetResultsToolResult = {
      id: output.origin?.id ?? "",
      input: output.input,
      output: output.output,
      metadata: "metadata" in output ? output.metadata : undefined,
      expected: output.expected,
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      scores: Object.fromEntries(
        Object.entries(output?.scores ?? {})
          .filter(([_, score]) => score !== null)
          .map(([key, score]) => [key, roundNumber(score)]),
      ) as Record<string, number>,
      metrics: {}, // TODO: The SDK doesn't give us these metrics.
    };
    results.push(result);
  }
  return results;
}
