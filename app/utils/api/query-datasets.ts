import { useQuery } from "@tanstack/react-query";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { useAuth } from "@clerk/nextjs";
import { type GetToken } from "@clerk/types";
import { useOrg } from "#/utils/user";
import { useMemo } from "react";
import { type getProjectContextDatasets } from "#/app/app/[org]/p/[project]/project-actions";
import { DEFAULT_PAGE_LIMIT } from "./pagination";

export function useOrgDatasetsApiQuery() {
  const { name: orgName } = useOrg();
  const { getToken } = useAuth();

  const { queryKey, queryFn } = useMemo(() => {
    return orgDatasetsApiQuery({
      orgName,
      getToken,
    });
  }, [orgName, getToken]);

  const result = useQuery({
    queryKey,
    queryFn,
  });

  return { result, queryKey };
}

const MAX_ITERATIONS = 4;

export function orgDatasetsApiQuery(queryKeyParams: {
  orgName: string;
  getToken: GetToken;
}) {
  const queryKey = [
    "getProjectContextDatasets",
    queryKeyParams.orgName,
    queryKeyParams.getToken,
  ];
  const queryFn = async ({ signal }: { signal: AbortSignal }) => {
    const results = [];

    let cursor: string | undefined;
    for (let i = 0; i < MAX_ITERATIONS; i++) {
      const queryResults = await invokeServerAction<
        typeof getProjectContextDatasets
      >({
        fName: "getProjectContextDatasets",
        args: {
          org_name: queryKeyParams.orgName,
          paginationParams: {
            starting_after: cursor,
          },
        },
        getToken: queryKeyParams.getToken,
        signal,
      });

      results.push(...queryResults);
      const nextCursor = queryResults.at(-1)?.id;
      if (nextCursor === cursor) {
        break;
      }
      cursor = nextCursor;
      if (queryResults.length < DEFAULT_PAGE_LIMIT) {
        break;
      }
    }
    return results;
  };

  return { queryKey, queryFn };
}
