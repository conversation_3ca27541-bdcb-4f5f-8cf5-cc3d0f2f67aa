import { type RefObject, useCallback, useMemo } from "react";
import {
  flexRender,
  type Table,
  type Cell,
  type CellContext,
  type Column,
  type ColumnDef,
  type Row,
  type VisibilityState,
} from "@tanstack/react-table";
import { cn } from "#/utils/classnames";
import { EXPERIMENT_COMPARISON_COLOR_CLASSNAMES } from "#/ui/charts/colors";
import {
  ArrowDown10,
  ArrowUp10,
  AppWindowMac,
  ArrowDownAZ,
  ArrowUpZA,
  ArrowDownRight,
  ArrowUpDown,
  ArrowUpRight,
  Blocks,
  Braces,
  Calendar,
  Equal,
  type LucideIcon,
  MessageSquareText,
  Percent,
  Tag,
  Timer,
  AlertCircle,
  Play,
  AlertTriangle,
} from "lucide-react";
import { NullFormatter } from "./null-formatter";
import {
  GroupKeyFormatter,
  type GroupKeyFormatterType,
} from "./group-key-formatter";
import { type FormatterProps } from "#/ui/arrow-table";
import {
  DiffLeftField,
  type DiffObjectType,
  DiffRightField,
} from "#/utils/diffs/diff-objects";
import {
  useDiffModeState,
  usePlaygroundPromptSheetIndexState,
} from "#/ui/query-parameters";
import { type SpanIFrame } from "@braintrust/typespecs";
import { IFrameViewer } from "#/ui/iframe-viewer";
import {
  deserializePlainStringAsJSON,
  isEmpty,
  isObject,
  safeDeserializePlainStringAsJSON,
  safeDeserializeUnknown,
  serializeJSONWithPlainString,
} from "#/utils/object";
import { BT_IS_GROUP } from "#/ui/table/grouping/queries";
import { type RowComparisonFormatterProps } from "./row-comparison-formatter";
import { toast } from "sonner";
import {
  type StreamingContentProps,
  useHasStreamingDataOrError,
  useStreamingNode,
} from "#/ui/table/cells/streaming";
import { Skeleton } from "#/ui/skeleton";
import {
  isPlaygroundRunningAtom,
  runningInfoAtom,
} from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/atoms";
import { useAtomValue } from "jotai";
import {
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "#/ui/dropdown-menu";
import { ColumnMenu } from "#/ui/table/column-menu";
import { type SortComparison } from "#/utils/search/search";
import { spanTypeInfoHasError } from "#/ui/virtual-table-body";
import { Button } from "#/ui/button";
import { isJSON } from "#/utils/is-json";
import { type PlayXRunPromptsArgs } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/playx";
import { usePromptTransactionIds } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/use-prompt-session";
import { BasicTooltip } from "#/ui/tooltip";
import { TRANSACTION_ID_FIELD } from "braintrust/util";
import { parseGridLayoutRow } from "#/ui/table/cells/parse-row";
import { MD_PROSE_CLASS_NAMES } from "#/ui/markdown";
import { AttachmentListFromData } from "#/ui/attachment/attachment-list";
import { FixWithLoopButton } from "#/ui/table/formatters/fix-with-loop-button";
import { Spinner } from "#/ui/icons/spinner";
import { type CustomColumnDefinition } from "#/utils/custom-columns/use-custom-columns";

const maskFieldStyle = {
  maskImage: "linear-gradient(to bottom, black calc(100% - 16px), transparent)",
};

export const GRID_INPUT_COLUMN_ID = "__grid_input";
export const GRID_EXPECTED_COLUMN_ID = "__grid_expected";
export const GRID_METADATA_COLUMN_ID = "__grid_metadata";
export const GRID_TAGS_COLUMN_ID = "__grid_tags";

export type SetColumnSortFn<TsData> = (
  col: Column<TsData, unknown>,
  desc: boolean,
  comparison?: SortComparison,
) => void;

export function gridInputColumn<
  TsData extends GroupKeyFormatterType,
  TsValue,
>(opts?: {
  isPlayground?: boolean;
  setColumnSort: SetColumnSortFn<TsData>;
}): ColumnDef<TsData, TsValue> {
  const HeaderComponent = ({ table }: { table: Table<TsData> }) => {
    const column = table.getColumn("input");
    return (
      <div className="flex items-center gap-1 text-xs font-medium">
        <ArrowDownRight className="size-3 text-primary-400" />
        Input
        <ColumnMenu
          isSortable
          onClickAsc={
            column && opts?.setColumnSort
              ? () => {
                  column.toggleSorting(false);
                  opts.setColumnSort(column, false);
                }
              : undefined
          }
          onClickDesc={
            column && opts?.setColumnSort
              ? () => {
                  column.toggleSorting(true);
                  opts.setColumnSort(column, true);
                }
              : undefined
          }
        />
      </div>
    );
  };

  return {
    id: GRID_INPUT_COLUMN_ID,
    header: HeaderComponent,
    size: 400,
    minSize: 400,
    meta: {
      isGridLayout: true,
      ignoreMultilineRendering: true,
      moreText: true,
      pinnedColumnIndex: 1,
      name: "input",
      type: "input",
      path: ["input"],
      header: HeaderComponent,
    },
    cell: (props) => {
      const Formatter = GroupKeyFormatter({
        Formatter: (
          formatterProps: FormatterProps<GroupKeyFormatterType, TsValue>,
        ) => {
          const columns = formatterProps.table.getAllColumns();
          const cells = formatterProps.row.getAllCells();
          const value = cells.find((c) => c.column.id === "input")?.getValue();
          const jsonValue =
            typeof value === "string" && isJSON(value)
              ? safeDeserializeUnknown(value)
              : null;
          return (
            <div className="flex size-full flex-col gap-1 pt-0.5">
              {!opts?.isPlayground && (
                <div className="mb-1 flex-none text-xs text-primary-600">
                  <ColumnValue
                    columnId="span_type_info"
                    columns={columns}
                    cells={cells}
                    value={cells
                      .find((c) => c.column.id === "span_type_info")
                      ?.getValue()}
                  />
                </div>
              )}
              <div className="flex-1 overflow-hidden" style={maskFieldStyle}>
                {jsonValue && (
                  <AttachmentListFromData
                    className="mb-2"
                    layout="dense"
                    data={jsonValue}
                  />
                )}
                <ColumnValue
                  columnId="input"
                  columns={columns}
                  cells={cells}
                  value={value}
                />
              </div>
            </div>
          );
        },
        groupKey: "span_type_info",
      });
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- https://github.com/TanStack/table/discussions/4391#discussioncomment-3683627
      const formatterProps = props as unknown as FormatterProps<
        GroupKeyFormatterType,
        TsValue
      >;
      return <Formatter {...formatterProps} />;
    },
  };
}

export function gridExpectedColumn<
  TsData extends GroupKeyFormatterType,
  TsValue,
>({
  setColumnSort,
}: {
  setColumnSort: SetColumnSortFn<TsData>;
}): ColumnDef<TsData, TsValue> {
  const HeaderComponent = ({ table }: { table: Table<TsData> }) => {
    const column = table.getColumn("expected");
    return (
      <div className="flex items-center gap-1 text-xs font-medium">
        <Equal className="size-3 text-primary-400" />
        Expected
        <ColumnMenu
          isSortable
          onClickAsc={
            column
              ? () => {
                  column.toggleSorting(false);
                  setColumnSort(column, true);
                }
              : undefined
          }
          onClickDesc={
            column
              ? () => {
                  column.toggleSorting(true);
                  setColumnSort(column, false);
                }
              : undefined
          }
          onHide={() => {
            table.setColumnVisibility((prev) => ({
              ...prev,
              expected: false,
            }));
          }}
        />
      </div>
    );
  };

  return {
    id: GRID_EXPECTED_COLUMN_ID,
    header: HeaderComponent,
    size: 400,
    minSize: 200,
    meta: {
      isGridLayout: true,
      ignoreMultilineRendering: true,
      moreText: true,
      name: "expected",
      type: "expected",
      path: ["expected"],
      header: HeaderComponent,
    },
    cell: (props) => {
      const columns = props.table.getAllColumns();
      const cells = props.row.getAllCells();
      return (
        <div className="flex size-full flex-col gap-1 pt-0.5">
          <div className="flex-1 overflow-hidden" style={maskFieldStyle}>
            <ColumnValue
              columnId="input"
              columns={columns}
              cells={cells}
              value={cells.find((c) => c.column.id === "expected")?.getValue()}
            />
          </div>
        </div>
      );
    },
  };
}

export function gridMetadataColumn<
  TsData extends GroupKeyFormatterType,
  TsValue,
>({
  setColumnSort,
}: {
  setColumnSort: SetColumnSortFn<TsData>;
}): ColumnDef<TsData, TsValue> {
  const HeaderComponent = ({ table }: { table: Table<TsData> }) => {
    const column = table.getColumn("metadata");
    return (
      <div className="flex items-center gap-1 text-xs font-medium">
        <Braces className="size-3 text-primary-400" />
        Metadata
        <ColumnMenu
          isSortable
          onClickAsc={() => {
            if (!column) return;
            column.toggleSorting(false);
            setColumnSort(column, true);
          }}
          onClickDesc={() => {
            if (!column) return;
            column.toggleSorting(true);
            setColumnSort(column, false);
          }}
          onHide={() => {
            table.setColumnVisibility((prev) => ({
              ...prev,
              metadata: false,
            }));
          }}
        />
      </div>
    );
  };

  return {
    id: GRID_METADATA_COLUMN_ID,
    header: HeaderComponent,
    size: 400,
    minSize: 200,
    meta: {
      isGridLayout: true,
      ignoreMultilineRendering: true,
      moreText: true,
      name: "metadata",
      type: "metadata",
      path: ["metadata"],
      header: HeaderComponent,
    },
    cell: (props) => {
      const columns = props.table.getAllColumns();
      const cells = props.row.getAllCells();
      return (
        <div className="flex size-full flex-col gap-1 pt-0.5">
          <div className="flex-1 overflow-hidden" style={maskFieldStyle}>
            <ColumnValue
              columnId="input"
              columns={columns}
              cells={cells}
              value={cells.find((c) => c.column.id === "metadata")?.getValue()}
            />
          </div>
        </div>
      );
    },
  };
}

export function gridTagsColumn<TsData extends GroupKeyFormatterType, TsValue>({
  setColumnSort,
}: {
  setColumnSort: SetColumnSortFn<TsData>;
}): ColumnDef<TsData, TsValue> {
  const HeaderComponent = ({ table }: { table: Table<TsData> }) => {
    const column = table.getColumn("tags");
    return (
      <div className="flex items-center gap-1 text-xs font-medium">
        <Tag className="size-3 text-primary-400" />
        Tags
        <ColumnMenu
          isSortable
          onClickAsc={() => {
            if (!column) return;
            column.toggleSorting(false);
            setColumnSort(column, false);
          }}
          onClickDesc={() => {
            if (!column) return;
            column.toggleSorting(true);
            setColumnSort(column, true);
          }}
          onHide={() => {
            table.setColumnVisibility((prev) => ({
              ...prev,
              tags: false,
            }));
          }}
        />
      </div>
    );
  };

  return {
    id: GRID_TAGS_COLUMN_ID,
    header: HeaderComponent,
    size: 200,
    minSize: 150,
    meta: {
      isGridLayout: true,
      ignoreMultilineRendering: true,
      moreText: true,
      name: "tags",
      type: "tags",
      path: ["tags"],
      header: HeaderComponent,
    },
    cell: (props) => {
      const columns = props.table.getAllColumns();
      const cells = props.row.getAllCells();
      return (
        <div className="flex size-full flex-col gap-1 pt-0.5">
          <div className="flex-1 overflow-hidden" style={maskFieldStyle}>
            <ColumnValue
              columnId="tags"
              columns={columns}
              cells={cells}
              value={cells.find((c) => c.column.id === "tags")?.getValue()}
            />
          </div>
        </div>
      );
    },
  };
}

const SortMenuSubItem = <TsData extends object>({
  label,
  Icon,
  setColumnSort,
  column,
  isNumeric,
  comparisonId,
}: {
  Icon?: LucideIcon;
  label: string;
  isNumeric?: boolean;
  column?: Column<TsData, unknown>;
  setColumnSort: SetColumnSortFn<TsData>;
  comparisonId?: string;
}) => {
  const [diffModeState, _setDiffModeState] = useDiffModeState();
  const isDiffEnabled =
    diffModeState.enabled &&
    diffModeState.enabledValue === "between_experiments";
  if (!column) return null;
  return (
    <DropdownMenuSub>
      <DropdownMenuSubTrigger>
        {Icon ? (
          <Icon className="size-3" />
        ) : (
          <ArrowUpDown className="size-3" />
        )}
        {label}
      </DropdownMenuSubTrigger>
      <DropdownMenuSubContent>
        <DropdownMenuItem
          onSelect={() => {
            column.toggleSorting(false);
            setColumnSort(
              column,
              false,
              comparisonId
                ? {
                    experimentId: comparisonId,
                    type: "value",
                  }
                : undefined,
            );
          }}
        >
          {isNumeric ? (
            <ArrowDown10 className="size-3" />
          ) : (
            <ArrowDownAZ className="size-3" />
          )}
          Sort ascending{isNumeric ? "" : " (A→Z)"}
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={() => {
            column.toggleSorting(true);
            setColumnSort(
              column,
              true,
              comparisonId
                ? {
                    experimentId: comparisonId,
                    type: "value",
                  }
                : undefined,
            );
          }}
        >
          {isNumeric ? (
            <ArrowUp10 className="size-3" />
          ) : (
            <ArrowUpZA className="size-3" />
          )}
          Sort descending{isNumeric ? "" : " (Z→A)"}
        </DropdownMenuItem>
        {isDiffEnabled && isNumeric && comparisonId && (
          <>
            <DropdownMenuItem
              onSelect={() => {
                column.toggleSorting(false);
                setColumnSort(
                  column,
                  false,
                  comparisonId
                    ? {
                        experimentId: comparisonId,
                        type: "score",
                      }
                    : undefined,
                );
              }}
            >
              {isNumeric ? (
                <ArrowDown10 className="size-3" />
              ) : (
                <ArrowDownAZ className="size-3" />
              )}
              Sort difference ascending{isNumeric ? "" : " (A→Z)"}
            </DropdownMenuItem>
            <DropdownMenuItem
              onSelect={() => {
                column.toggleSorting(true);
                setColumnSort(
                  column,
                  true,
                  comparisonId
                    ? {
                        experimentId: comparisonId,
                        type: "score",
                      }
                    : undefined,
                );
              }}
            >
              {isNumeric ? (
                <ArrowUp10 className="size-3" />
              ) : (
                <ArrowUpZA className="size-3" />
              )}
              Sort difference descending{isNumeric ? "" : " (Z→A)"}
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuSubContent>
    </DropdownMenuSub>
  );
};

export function GridExperimentHeader<TsData extends object>({
  dotClassName,
  experimentName,
  experimentId,
  isBase,
  setColumnSort,
  columns,
  context,
  colIdx,
  traceRowId,
  runRef,
  runPrompts,
  isPending,
}: {
  dotClassName: string;
  experimentName: string;
  experimentId?: string;
  isBase: boolean;
  setColumnSort: SetColumnSortFn<TsData>;
  columns: Column<TsData, unknown>[];
  isPlayground?: boolean;
  runPrompts?: (args: PlayXRunPromptsArgs) => void;
  context:
    | "playground-table-header"
    | "playground-trace-header"
    | "experiment-table-header";
  colIdx?: number;
  traceRowId?: string;
  runRef?: RefObject<{
    onRun: (colIdx: number) => void;
  } | null>;
  isPending?: boolean;
}) {
  const scoreColumns = useMemo(
    () => columns.filter((c) => c.id.startsWith("scores.")),
    [columns],
  );
  const { setPromptSheetIndex } = usePlaygroundPromptSheetIndexState();

  const isPlayground =
    context === "playground-table-header" ||
    context === "playground-trace-header";

  const isRunningOrStreaming = useAtomValue(isPlaygroundRunningAtom);
  const runPromptMenuItem = isPlayground && !isEmpty(colIdx) && runPrompts && (
    <DropdownMenuItem
      onSelect={() =>
        runPrompts({
          colIdx,
        })
      }
      disabled={isRunningOrStreaming}
    >
      <Play className="size-3 flex-none" />
      Run column
    </DropdownMenuItem>
  );

  const hasColumns = columns.length > 0;

  const showColumnMenu =
    (context === "playground-table-header" ||
      context === "experiment-table-header") &&
    (hasColumns || runPromptMenuItem);
  const comparisonId = !isBase && experimentId ? experimentId : undefined;

  return (
    <span
      className={cn(
        "flex flex-1 items-center w-full overflow-hidden gap-1.5 pr-0 text-xs transition-all",
        {
          "group-hover:pr-5": showColumnMenu,
        },
      )}
    >
      <span className={cn("size-2 flex-none rounded-full", [dotClassName])} />
      <span className="flex-1 truncate">
        {experimentName === "Select model"
          ? "No model selected"
          : experimentName}
      </span>
      {isPending && (
        <Spinner className="size-3 min-w-7 flex-none text-primary-400" />
      )}
      <span className="flex-none text-xs font-normal text-primary-400">
        {isBase ? "Base" : "Comparison"}
      </span>
      {context === "playground-trace-header" && runRef?.current?.onRun && (
        <BasicTooltip tooltipContent="Run column">
          <div>
            <Button
              size="xs"
              Icon={Play}
              variant="default"
              className="ml-1 text-primary-500"
              disabled={isRunningOrStreaming}
              onClick={() => {
                // Inside of the playground cell, use runRef which has more context
                // about the selected row
                if (!isEmpty(colIdx)) {
                  runRef.current?.onRun(colIdx);
                } else {
                  throw new Error("runRef is not defined");
                }
              }}
            />
          </div>
        </BasicTooltip>
      )}
      {showColumnMenu && (
        <ColumnMenu
          onEditTask={
            isPlayground && colIdx !== undefined
              ? () => {
                  setPromptSheetIndex(colIdx);
                }
              : undefined
          }
        >
          {runPromptMenuItem}
          <SortMenuSubItem
            label="Sort by output"
            Icon={ArrowUpRight}
            column={columns.find((c) => c.id === "output")}
            setColumnSort={setColumnSort}
            comparisonId={comparisonId}
          />
          {!isPlayground && (
            <SortMenuSubItem
              label="Sort by expected"
              Icon={Equal}
              column={columns.find((c) => c.id === "expected")}
              setColumnSort={setColumnSort}
              comparisonId={comparisonId}
            />
          )}
          {scoreColumns.map((c) => (
            <SortMenuSubItem
              key={c.id}
              label={`Sort by ${c.id.split(".").slice(1).join(" ")}`}
              Icon={Percent}
              isNumeric
              column={c}
              setColumnSort={setColumnSort}
              comparisonId={comparisonId}
            />
          ))}
          <SortMenuSubItem
            label="Sort by tags"
            Icon={Tag}
            column={columns.find((c) => c.id === "tags")}
            setColumnSort={setColumnSort}
          />
          {!isPlayground && (
            <SortMenuSubItem
              label="Sort by metadata"
              Icon={Braces}
              column={columns.find((c) => c.id === "metadata")}
              setColumnSort={setColumnSort}
              comparisonId={comparisonId}
            />
          )}
        </ColumnMenu>
      )}
    </span>
  );
}

export function gridBaseColumn<
  TsData extends { [BT_IS_GROUP]?: boolean },
  TsValue,
>({
  experimentName,
  columnVisibility,
  customColumns,
  streamingContentProps,
  tableRowHeight,
  isPlayground,
  isLoopEnabled,
  setColumnSort,
  removeLimiter,
  index,
}: {
  experimentName: string;
  columnVisibility: VisibilityState;
  customColumns?: string[];
  streamingContentProps?: StreamingContentProps;
  tableRowHeight: "compact" | "tall";
  isPlayground?: boolean;
  isLoopEnabled?: boolean;
  setColumnSort: SetColumnSortFn<TsData>;
  removeLimiter?: boolean;
  index: number;
}): ColumnDef<TsData, TsValue | null> {
  const HeaderComponent = ({ table }: { table: Table<TsData> }) => (
    <GridExperimentHeader
      dotClassName="bg-primary-500"
      experimentName={experimentName}
      isBase
      setColumnSort={setColumnSort}
      columns={table.getAllLeafColumns()}
      isPlayground={isPlayground}
      runPrompts={streamingContentProps?.runPrompts}
      context={
        isPlayground ? "playground-table-header" : "experiment-table-header"
      }
      colIdx={0}
    />
  );
  return {
    id: "__grid_base",
    enableHiding: false,
    header: HeaderComponent,
    size: 400,
    minSize: 400,
    cell: (props) => {
      if (props.row.original[BT_IS_GROUP]) {
        return null;
      }
      return (
        <GridCell
          columns={props.table.getAllColumns()}
          cells={props.row.getAllCells()}
          row={props.row}
          columnVisibility={columnVisibility}
          customColumns={customColumns}
          formatterProps={props}
          streamingContentProps={streamingContentProps}
          tableRowHeight={tableRowHeight}
          isPlayground={isPlayground}
          isLoopEnabled={isLoopEnabled}
          removeLimiter={removeLimiter}
        />
      );
    },
    accessorFn: () => {
      // return null for grouping purposes,
      // since we don't actually ever get any value for this column otherwise
      return null;
    },
    meta: {
      header: HeaderComponent,
      isGridLayout: true,
      ignoreMultilineRendering: true,
      moreText: true,
      pinnedColumnIndex: 2,
      name: "Base",
      type: "base",
      path: ["base"],
    },
  };
}

export function gridComparisonColumns<
  TsData extends { [BT_IS_GROUP]?: boolean },
  TsValue,
>({
  comparisonExperiments,
  columnVisibility,
  rowComparisonProps,
  customColumns,
  streamingContentProps,
  tableRowHeight,
  isPlayground,
  isLoopEnabled,
  setColumnSort,
  removeLimiter,
}: {
  comparisonExperiments?: { name: string; id: string }[];
  columnVisibility: VisibilityState;
  rowComparisonProps?: RowComparisonFormatterProps;
  customColumns?: string[];
  streamingContentProps?: StreamingContentProps;
  tableRowHeight: "compact" | "tall";
  isPlayground?: boolean;
  isLoopEnabled?: boolean;
  setColumnSort: SetColumnSortFn<TsData>;
  removeLimiter?: boolean;
}): ColumnDef<TsData, TsValue | null>[] {
  if (!comparisonExperiments) {
    return [];
  }
  return comparisonExperiments.map((experiment, index) => {
    const HeaderComponent = ({ table }: { table: Table<TsData> }) => (
      <GridExperimentHeader
        dotClassName={EXPERIMENT_COMPARISON_COLOR_CLASSNAMES[index]}
        experimentName={experiment.name}
        experimentId={experiment.id}
        isBase={false}
        setColumnSort={setColumnSort}
        columns={table.getAllLeafColumns()}
        isPlayground={isPlayground}
        runPrompts={streamingContentProps?.runPrompts}
        context={
          isPlayground ? "playground-table-header" : "experiment-table-header"
        }
        colIdx={index + 1}
      />
    );
    return {
      id: `__grid_comparison_${index}`,
      enableHiding: false,
      header: HeaderComponent,
      size: 400,
      minSize: 400,
      cell: (props) => {
        if (props.row.original[BT_IS_GROUP]) {
          return null;
        }
        return (
          <GridCell
            columns={props.table.getAllColumns()}
            cells={props.row.getAllCells()}
            comparisonIndex={index}
            row={props.row}
            columnVisibility={columnVisibility}
            rowComparisonProps={rowComparisonProps}
            customColumns={customColumns}
            formatterProps={props}
            streamingContentProps={streamingContentProps}
            tableRowHeight={tableRowHeight}
            isPlayground={isPlayground}
            isLoopEnabled={isLoopEnabled}
            removeLimiter={removeLimiter}
          />
        );
      },
      accessorFn: () => {
        // return null for grouping purposes,
        // since we don't actually ever get any value for this column otherwise
        return null;
      },
      meta: {
        header: HeaderComponent,
        isGridLayout: true,
        ignoreMultilineRendering: true,
        moreText: true,
        name: `e${index + 2}`,
        type: "comparison",
        path: [`e${index + 2}`],
      },
    };
  });
}

export function gridSpanIframeFields<TsData, TsValue>({
  spanIframes,
}: {
  spanIframes: SpanIFrame[];
}): ColumnDef<TsData, TsValue | null>[] {
  return spanIframes.map((spanIframe, index) => {
    const HeaderComponent = () => (
      <span className="flex items-center gap-1.5">
        <AppWindowMac className="size-3 flex-none text-primary-500" />
        <span className="flex-1 truncate">{spanIframe.name}</span>
      </span>
    );
    return {
      id: `spaniframes.${spanIframe.id}`,
      header: HeaderComponent,
      cell: (props) => {
        return (
          <IFrameViewer
            urlTemplate={spanIframe.url}
            postMessage={spanIframe.post_message ?? false}
            // Roundtrip this to get rid of arrow encoding
            value={safeDeserializePlainStringAsJSON(
              serializeJSONWithPlainString(props.row.original),
            )}
            onUpdate={() => {
              toast("Spans cannot be updated from table cells");
            }}
          />
        );
      },
      accessorFn: () => {
        return null;
      },
      meta: {
        header: HeaderComponent,
        ignoreMultilineRendering: true,
        moreText: true,
        name: spanIframe.name,
        type: "span_iframe",
        path: [spanIframe.id],
      },
    };
  });
}

export function GridCell<TsData extends object, TsValue>({
  columns,
  cells,
  comparisonIndex,
  row,
  columnVisibility,
  rowComparisonProps,
  customColumns,
  formatterProps,
  streamingContentProps,
  tableRowHeight,
  isPlayground,
  isLoopEnabled,
  removeLimiter,
}: {
  columns: Column<TsData, unknown>[];
  cells: Cell<TsData, unknown>[];
  comparisonIndex?: number;
  row: Row<TsData>;
  columnVisibility?: VisibilityState;
  rowComparisonProps?: RowComparisonFormatterProps;
  customColumns?: string[];
  formatterProps: CellContext<TsData, TsValue>;
  streamingContentProps?: StreamingContentProps;
  tableRowHeight: "compact" | "tall";
  isPlayground?: boolean;
  isLoopEnabled?: boolean;
  removeLimiter?: boolean;
}) {
  const [diffModeState, _setDiffModeState] = useDiffModeState();
  const isDiffEnabled =
    diffModeState.enabled &&
    diffModeState.enabledValue === "between_experiments";

  const getValue = useCallback(
    (
      columnId: string,
      opts?: { truncate?: boolean; originalValue?: boolean },
    ) => {
      const splitColumnId = columnId.split(".");
      if (comparisonIndex != null) {
        const comparisonString = `e${comparisonIndex + 2}`;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions
        const comparisonRow = (row.original as any).__bt_internal?.[
          comparisonString
        ]?.data;
        if (splitColumnId.length === 1) {
          return comparisonRow?.[columnId];
        }
        return comparisonRow?.[splitColumnId[0]]?.[splitColumnId[1]];
      }

      let v = null;
      if (opts?.originalValue) {
        const splitColumnId = columnId.split(".");
        v =
          splitColumnId.length === 1
            ? // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions
              (row.original as any)[columnId]
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions
              (row.original as any)?.[splitColumnId[0]]?.[splitColumnId[1]];
      } else {
        const cell = cells.find((c) => c.column.id === columnId);
        const cellValue = cell?.getValue();
        const { value, mergedValues } =
          isObject(cellValue) && "mergedValues" in cellValue
            ? cellValue
            : { value: cellValue, mergedValues: undefined };
        const mergedValue =
          isObject(mergedValues) && Object.keys(mergedValues).length
            ? ((() => {
                const elem = Object.values(mergedValues)[0];
                return elem && isObject(elem) ? elem.value : undefined;
              })() ?? value)
            : undefined;

        v = mergedValue ?? value;
      }

      if (opts?.truncate && typeof v === "string") {
        return v.slice(0, 2000);
      }

      return v;
    },
    [comparisonIndex, row.original, cells],
  );

  const getScoreDiffObject = useCallback(
    (columnId: string) => {
      if (!isDiffEnabled || comparisonIndex == null) return undefined;

      const isScore = columnId.startsWith("scores");
      if (!isScore) {
        return undefined;
      }

      const splitColumnId = columnId.split(".");
      const scoreName =
        splitColumnId.length > 0 ? splitColumnId.slice(1).join(".") : columnId;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions
      const baseRow = (row.original as any)?.scores?.[scoreName];

      return {
        [DiffLeftField]: undefined,
        [DiffRightField]: baseRow,
      };
    },
    [isDiffEnabled, comparisonIndex, row.original],
  );

  const scoreColumns = useMemo(
    () => columns.filter((c) => c.id.startsWith("scores.")),
    [columns],
  );

  const spanIframeColumns = useMemo(
    () => columns.filter((c) => c.id.startsWith("spaniframes.")),
    [columns],
  );

  const outputVsExpectedColumn = useMemo(
    () => columns.find((c) => c.id === "output_vs_expected"),
    [columns],
  );

  const visibleColumns = useMemo(() => {
    const isColumnVisible = (columnId: string) => {
      const visibility = columnVisibility?.[columnId];
      return visibility == null || !!visibility;
    };

    const cols = new Set<string>();
    if (outputVsExpectedColumn) {
      cols.add("output_vs_expected");
    } else {
      cols.add("output");
      if (isColumnVisible("expected") && !isPlayground) {
        cols.add("expected");
      }
    }

    scoreColumns.forEach((scoreColumn) => {
      if (isColumnVisible(scoreColumn.id)) {
        cols.add(scoreColumn.id);
      }
    });

    if (isColumnVisible("tags") && !isPlayground) {
      cols.add("tags");
    }
    if (isColumnVisible("comments") && !isPlayground) {
      cols.add("comments");
    }
    if (isColumnVisible("metadata") && !isPlayground) {
      cols.add("metadata");
    }

    customColumns?.forEach((col) => {
      if (isColumnVisible(col)) {
        cols.add(col);
      }
    });

    spanIframeColumns.forEach((spanIframeColumn) => {
      if (isColumnVisible(spanIframeColumn.id)) {
        cols.add(spanIframeColumn.id);
      }
    });

    return cols;
  }, [
    columnVisibility,
    scoreColumns,
    spanIframeColumns,
    outputVsExpectedColumn,
    isPlayground,
    customColumns,
  ]);

  const isVisible = useCallback(
    (columnId: string) => {
      return visibleColumns.has(columnId);
    },
    [visibleColumns],
  );

  const isPlaygroundRunning = useAtomValue(isPlaygroundRunningAtom);

  const {
    hasStreamingData,
    error: streamError,
    consoleMessages,
    datasetRowId,
    generationId,
    isTaskDone,
    streamingStatus,
  } = useHasStreamingDataOrError({
    colName: "output",
    rowOriginal: row.original,
    index: comparisonIndex != null ? comparisonIndex + 1 : 0,
    streamingContentProps,
    isGridLayout: true,
  });

  const runningInfo = useAtomValue(runningInfoAtom);

  if (visibleColumns.size === 0) {
    // This shouldn't be possible right now, but keeping it here in case we want to allow
    // all columns to be hidden in the future
    return (
      <div className="flex h-full items-center text-xs text-primary-500">
        All fields are hidden
      </div>
    );
  }

  const staleIndicator = (
    <StaleIndicator
      isPlaygroundRunning={isPlaygroundRunning}
      rowOriginal={row.original}
      index={comparisonIndex != null ? comparisonIndex + 1 : 0}
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions
      datasetRowTransactionId={(row.original as any)[TRANSACTION_ID_FIELD]}
    />
  );

  const lastConsoleMessage =
    consoleMessages.length > 0
      ? consoleMessages[consoleMessages.length - 1]
      : null;

  const isRunningCell =
    (!runningInfo.generationIds ||
      runningInfo.generationIds.includes(generationId)) &&
    (!runningInfo.rowIds || runningInfo.rowIds.includes(datasetRowId));
  const endTime = getValue("metrics.end");
  const showPlaygroundSkeleton =
    isPlayground && isPlaygroundRunning && isRunningCell && endTime == null;

  const metricsMessage = isTaskDone
    ? "Evaluating..."
    : hasStreamingData
      ? `${streamingStatus}...`
      : "Pending...";

  const isThinking = metricsMessage === "Thinking...";

  const metricsContent = !showPlaygroundSkeleton ? (
    <div className="flex items-center gap-3 pr-3">
      <OutputCellMetric
        columnId="created"
        columns={columns}
        cells={cells}
        value={getValue("metrics.start") * 1000}
        Icon={Calendar}
        title="Created"
      />
      <OutputCellMetric
        columnId="metrics.duration"
        columns={columns}
        cells={cells}
        value={getValue("metrics.duration")}
        Icon={Timer}
        title="Duration"
      />
      <OutputCellMetric
        columnId="metrics.completion_tokens"
        columns={columns}
        cells={cells}
        value={getValue("metrics.completion_tokens")}
        Icon={Blocks}
        title="Completion tokens"
      />
      <OutputCellMetric
        columnId="metrics.estimated_cost"
        columns={columns}
        cells={cells}
        value={getValue("metrics.estimated_cost")}
        title="Estimated LLM cost"
      />
      {staleIndicator}
    </div>
  ) : (
    <div className="animate-textShimmer bg-gradient-to-r from-primary-300 via-primary-500 to-primary-300 bg-clip-text pr-3 text-xs text-transparent [animation-iteration-count:infinite] [animation-timing-function:linear]">
      {lastConsoleMessage?.message ?? metricsMessage}
    </div>
  );
  const outputValue = getValue(
    outputVsExpectedColumn ? "output_vs_expected" : "output",
    { truncate: true },
  );
  const hasError =
    !!streamError || spanTypeInfoHasError(getValue("span_type_info"));

  if (
    isPlayground &&
    outputValue == null &&
    !hasError &&
    !hasStreamingData &&
    !showPlaygroundSkeleton
  ) {
    return (
      <div className="pt-0.5 text-sm text-primary-400">
        This cell has not been run yet
      </div>
    );
  }

  if (hasError) {
    // Base columns display their error in output, comparison columns use the error field
    const cellError = comparisonIndex == null ? outputValue : getValue("error");
    return (
      <div className="group w-full pt-0.5">
        <div className="mb-1 flex items-center justify-between text-xs text-primary-600">
          {renderHeader({
            label: "Error",
            Icon: AlertCircle,
            className:
              "bg-bad-50 px-1.5 rounded border border-bad-100 dark:bg-bad-50/50 dark:border-bad-50/80 text-bad-600",
            iconClassName: "text-bad-600",
          })}

          <div className="flex items-center gap-1">
            {isLoopEnabled && isPlayground && (
              <FixWithLoopButton error={cellError} />
            )}
            {staleIndicator}
          </div>
        </div>
        <div
          className={cn("mb-2", {
            "h-[172px]": tableRowHeight !== "tall" && !removeLimiter,
            "h-[388px]": tableRowHeight === "tall" && !removeLimiter,
          })}
          style={removeLimiter ? undefined : maskFieldStyle}
        >
          <ColumnValue
            columnId="output"
            columns={columns}
            cells={cells}
            value={
              streamError ??
              (isJSON(cellError)
                ? deserializePlainStringAsJSON(cellError).value
                : cellError)
            }
            comparisonIndex={comparisonIndex}
            streamingContentProps={streamingContentProps}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="group relative w-full pt-0.5">
      {isVisible("output_vs_expected") && (
        <>
          <div className="mb-1 flex items-center justify-between text-xs text-primary-600">
            {renderHeader({
              label: "Output vs. expected",
              Icon: ArrowUpRight,
            })}
            {metricsContent}
          </div>
          <div
            className={cn("mb-2", {
              "h-[296px]": tableRowHeight !== "tall" && !removeLimiter,
              "h-[512px]": tableRowHeight === "tall" && !removeLimiter,
            })}
            style={maskFieldStyle}
          >
            {showPlaygroundSkeleton && !hasStreamingData ? (
              <CellSkeleton />
            ) : (
              <ColumnValue
                columnId="output_vs_expected"
                columns={columns}
                cells={cells}
                value={getValue("output_vs_expected", { truncate: true })}
              />
            )}
          </div>
        </>
      )}
      {isVisible("output") && (
        <>
          <div className="mb-1 flex items-center justify-between text-xs text-primary-600">
            {renderHeader({ label: "Output", Icon: ArrowUpRight })}
            {metricsContent}
          </div>
          <div
            className={cn("mb-2", {
              "h-[172px]": tableRowHeight !== "tall" && !removeLimiter,
              "h-[388px]": tableRowHeight === "tall" && !removeLimiter,
            })}
            style={removeLimiter ? undefined : maskFieldStyle}
          >
            {showPlaygroundSkeleton && !hasStreamingData ? (
              <CellSkeleton />
            ) : (
              <ColumnValue
                columnId="output"
                columns={columns}
                cells={cells}
                value={outputValue}
                comparisonIndex={comparisonIndex}
                streamingContentProps={streamingContentProps}
                streamingClassName={isThinking ? "text-primary-500" : undefined}
              />
            )}
          </div>
        </>
      )}
      {isVisible("expected") && (
        <>
          <div className="mb-1 text-xs text-primary-600">
            {renderHeader({ label: "Expected", Icon: Equal })}
          </div>
          <div
            className={cn("mb-2 h-24", {
              "h-auto": removeLimiter,
            })}
            style={maskFieldStyle}
          >
            <ColumnValue
              columnId="expected"
              columns={columns}
              cells={cells}
              value={getValue("expected", { truncate: true })}
            />
          </div>
        </>
      )}
      <div className="mb-2 flex flex-col gap-1 empty:hidden">
        {scoreColumns.map((s) => {
          if (!isVisible(s.id)) {
            return null;
          }
          const scoreValue = getValue(s.id);
          return (
            <div key={s.id} className="flex h-5 items-center gap-2">
              <div
                className="w-32 flex-none truncate text-xs text-primary-600"
                title={s.columnDef.meta?.name}
              >
                {renderHeader({
                  label: s.columnDef.meta?.name ?? s.id,
                  Icon: Percent,
                })}
              </div>
              {showPlaygroundSkeleton && scoreValue == null ? (
                <Skeleton className="h-4 w-full max-w-18 bg-primary-200" />
              ) : (
                <div className="flex-1 truncate">
                  <ColumnValue
                    columnId={s.id}
                    columns={columns}
                    cells={cells}
                    value={scoreValue}
                    valueDiffObject={getScoreDiffObject(s.id)}
                  />
                </div>
              )}
            </div>
          );
        })}
      </div>
      {isVisible("tags") && (
        <div className="mb-2 flex h-5 items-center gap-2">
          <div className="w-32 flex-none text-xs text-primary-600">
            {renderHeader({ label: "Tags", Icon: Tag })}
          </div>
          <div className="flex-1 truncate leading-none">
            <ColumnValue
              columnId="tags"
              columns={columns}
              cells={cells}
              value={getValue("tags")}
            />
          </div>
        </div>
      )}
      {isVisible("comments") && (
        <div className="mb-2 flex h-5 items-center gap-2">
          <div className="w-32 flex-none truncate text-xs text-primary-600">
            {renderHeader({ label: "Comments", Icon: MessageSquareText })}
          </div>
          <div className="flex-none truncate">
            <ColumnValue
              columnId="comments"
              columns={columns}
              cells={cells}
              value={getValue("comments")}
            />
          </div>
        </div>
      )}
      {isVisible("metadata") && (
        <div className="mb-2 flex flex-col gap-1">
          <div className="flex-none truncate text-xs text-primary-600">
            {renderHeader({ label: "Metadata", Icon: Braces })}
          </div>
          <div
            className={cn("h-24", {
              "h-auto": removeLimiter,
            })}
            style={removeLimiter ? undefined : maskFieldStyle}
          >
            <ColumnValue
              columnId="metadata"
              columns={columns}
              cells={cells}
              value={getValue("metadata", { truncate: true })}
            />
          </div>
        </div>
      )}
      {customColumns?.map(
        (col, i) =>
          isVisible(col) && (
            <div key={i} className="mb-2 flex flex-col gap-1">
              <div className="flex-none truncate text-xs text-primary-600">
                {renderHeader({ label: col, Icon: Braces })}
              </div>
              <div
                className={cn("h-24", {
                  "h-auto": removeLimiter,
                })}
                style={removeLimiter ? undefined : maskFieldStyle}
              >
                <ColumnValue
                  columnId={col}
                  columns={columns}
                  cells={cells}
                  value={getValue(col)}
                />
              </div>
            </div>
          ),
      )}
      {spanIframeColumns.length > 0 && (
        <div className="mb-2 flex flex-col gap-1 empty:hidden">
          {spanIframeColumns.map((spanIframeColumn) => {
            if (!isVisible(spanIframeColumn.id)) {
              return null;
            }
            return (
              <div key={spanIframeColumn.id} className="flex flex-col gap-2">
                <div
                  className="flex-none truncate text-xs text-primary-600"
                  title={spanIframeColumn.columnDef.meta?.name}
                >
                  {renderHeader({
                    label:
                      spanIframeColumn.columnDef.meta?.name ??
                      spanIframeColumn.id,
                    Icon: AppWindowMac,
                  })}
                </div>
                <ColumnValue
                  columnId={spanIframeColumn.id}
                  columns={columns}
                  cells={cells}
                  value={getValue(spanIframeColumn.id)}
                />
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

const OutputCellMetric = <TsData extends object>({
  columnId,
  value,
  columns,
  cells,
  Icon,
  title,
}: {
  columnId: string;
  value: unknown;
  columns: Column<TsData, unknown>[];
  cells: Cell<TsData, unknown>[];
  Icon?: LucideIcon;
  title: string;
}) => {
  if (isEmpty(value)) {
    return null;
  }
  return (
    <div className="flex items-center gap-1 text-primary-400" title={title}>
      {Icon && <Icon className="size-3" />}
      <ColumnValue
        columnId={columnId}
        columns={columns}
        cells={cells}
        value={value}
        disableGridLayoutMeta
      />
    </div>
  );
};

const CellSkeleton = () => (
  <div className="flex w-full flex-col gap-2 pt-0.5">
    <Skeleton className="h-7 w-[95%] bg-primary-200" />
    <Skeleton className="h-7 w-[90%] opacity-80 bg-primary-200" />
    <Skeleton className="h-7 w-4/5 opacity-60 bg-primary-200" />
    <Skeleton className="h-7 w-3/5 opacity-40 bg-primary-200" />
  </div>
);

const renderHeader = ({
  label,
  Icon,
  iconClassName,
  className,
}: {
  label: string;
  Icon: LucideIcon;
  iconClassName?: string;
  className?: string;
}) => {
  return (
    <div
      className={cn("flex flex-none items-center gap-1 truncate", className)}
    >
      <Icon className={cn("size-3 flex-none", iconClassName)} />
      <span className="flex-1 truncate">{label}</span>
    </div>
  );
};

const StaleIndicator = ({
  isPlaygroundRunning,
  rowOriginal,
  index,
  datasetRowTransactionId,
}: {
  rowOriginal: object;
  isPlaygroundRunning: boolean;
  index: number;
  datasetRowTransactionId?: string;
}) => {
  const promptTransactionIds = usePromptTransactionIds();

  const { spanTypeInfo, origin, data } = parseGridLayoutRow({
    rowOriginal,
    index,
  });

  if (!spanTypeInfo || (!origin && !datasetRowTransactionId)) {
    return null;
  }

  try {
    const currentPromptTransactionId =
      promptTransactionIds[spanTypeInfo.generation];
    const cellPromptTransactionId = data?.playground_xact_id;
    const hasNewPrompt =
      (currentPromptTransactionId &&
        cellPromptTransactionId &&
        currentPromptTransactionId > cellPromptTransactionId) ||
      // optimistic updates set the transaction id to 0, so this ensures that the stale indicator
      // is shown between optimistic update -> realtime update. however, running the playground flushes
      // potentially unaffected rows, so we don't want to show the stale indicator in that case.
      (currentPromptTransactionId === "0" && !isPlaygroundRunning);

    const originTransactionId = origin ? origin[TRANSACTION_ID_FIELD] : null;
    const hasNewDataset =
      (originTransactionId &&
        datasetRowTransactionId &&
        datasetRowTransactionId > originTransactionId) ||
      // optimistic updates set the transaction id to 0, so this ensures that the stale indicator
      // is shown between optimistic update -> realtime update
      (originTransactionId &&
        datasetRowTransactionId === "0" &&
        !isPlaygroundRunning);

    let tooltipContent;
    if (hasNewPrompt && hasNewDataset) {
      tooltipContent =
        "This prompt and dataset have changed since this cell was last run. Run the playground to view results for the latest prompt and dataset.";
    } else if (hasNewPrompt) {
      tooltipContent =
        "This prompt has changed since this cell was last run. Run the playground to view results for the latest prompt.";
    } else if (hasNewDataset) {
      tooltipContent =
        "This dataset has changed since this cell was last run. Run the playground to view results for the latest dataset.";
    }

    if (hasNewPrompt || hasNewDataset) {
      return (
        <BasicTooltip tooltipContent={tooltipContent}>
          <div className="flex items-center rounded-full text-amber-500 dark:text-amber-900">
            <AlertTriangle className="size-3" />
          </div>
        </BasicTooltip>
      );
    }
  } catch (e) {
    return null;
  }
};

function ColumnValue<TsData>({
  columnId,
  columns,
  cells,
  value,
  disableGridLayoutMeta,
  valueDiffObject,
  streamingContentProps,
  comparisonIndex,
  streamingClassName,
}: {
  columnId: string;
  columns: Column<TsData, unknown>[];
  cells: Cell<TsData, unknown>[];
  value: unknown;
  disableGridLayoutMeta?: boolean;
  valueDiffObject?: DiffObjectType<unknown>;
  streamingContentProps?: StreamingContentProps;
  comparisonIndex?: number;
  streamingClassName?: string;
}) {
  const column = columns.find((c) => c.id === columnId);
  const cell = cells.find((c) => c.column.id === columnId);

  const nodeWithStreamedContent = useStreamingNode({
    colName: column?.columnDef.id ?? "",
    rowOriginal: cell?.row.original,
    index: comparisonIndex != null ? comparisonIndex + 1 : 0,
    streamingContentProps,
    isGridLayout: true,
    className: cn(
      MD_PROSE_CLASS_NAMES,
      "whitespace-pre-line py-0",
      streamingClassName,
    ),
  });

  if (!column || !cell) {
    return <NullFormatter />;
  }

  if (nodeWithStreamedContent) {
    return nodeWithStreamedContent;
  }

  return flexRender(column.columnDef.cell, {
    inTable: true,
    setValue: () => undefined, // do not allow edits in table cells
    updateRow: undefined, // Do not allow edits in the table cells
    hideNulls: false,
    value,
    valueDiffObject,
    ...cell.getContext(),
    meta: { ...column.columnDef.meta, isGridLayout: !disableGridLayoutMeta },
  });
}

export function calculateFieldHeights<TsData, TsValue>({
  columns,
  columnVisibility,
  customColumns,
  isPlayground,
  tableRowHeight,
  isOutputVsExpected,
}: {
  columns: Column<TsData, TsValue>[];
  columnVisibility: Record<string, boolean>;
  customColumns?: CustomColumnDefinition[];
  isPlayground?: boolean;
  tableRowHeight: "compact" | "tall";
  isOutputVsExpected: boolean;
}) {
  const customColumnNames = customColumns?.map(({ name }) => name);
  const fieldNames = columns.flatMap((c) =>
    !c.id ||
    c.columnDef.meta?.isGridLayout ||
    columnVisibility[c.id] === false ||
    (isPlayground &&
      (c.id === "metadata" || c.id === "expected" || c.id === "tags"))
      ? []
      : [c.id],
  );

  const scores = fieldNames.filter((name) => name.startsWith("scores."));
  const scoresHeight = scores.length
    ? // score height
      scores.length * 20 +
      // gap
      (scores.length - 1) * 4 +
      // margin bottom
      (scores.length > 0 ? 8 : 0)
    : 0;

  const spanIframes = fieldNames.filter((name) =>
    name.startsWith("spaniframes."),
  );
  const spanIframesHeight = spanIframes.length
    ? // span iframe height
      spanIframes.length * 352 +
      // gap and margin bottom (both 8)
      (spanIframes.length > 0 ? spanIframes.length : 0) * 8
    : 0;

  const expectedHeight = 116;
  const [restHeight, restCount] = fieldNames.reduce(
    ([accHeight, count], f) => {
      const field = f.startsWith("scores.")
        ? "scores"
        : f.startsWith("metrics.")
          ? "metrics"
          : f.startsWith("spaniframes.")
            ? "spaniframes"
            : customColumnNames && customColumnNames.indexOf(f) > -1
              ? "custom"
              : f;

      let height = 0;
      switch (field) {
        case "spaniframes":
        case "scores":
        case "metrics":
          height = 0;
          break;
        case "expected":
          if (isOutputVsExpected) {
            height = 0;
            break;
          }
          height = expectedHeight;
          break;
        case "metadata":
          height = 116;
          break;
        case "comments":
          if (isPlayground) {
            height = 0;
            break;
          }
          height = 28;
          break;
        case "tags":
          height = 28;
          break;
        case "custom":
          height = 116;
          break;
        default:
          height = 0;
      }

      return [accHeight + height, height > 0 ? count + 1 : count];
    },
    [0, 0],
  );
  const baseOutputHeight = tableRowHeight === "tall" ? 416 : 200;
  const outputHeight = isOutputVsExpected
    ? baseOutputHeight + expectedHeight + 8
    : baseOutputHeight;

  return Math.max(
    outputHeight +
      scoresHeight +
      spanIframesHeight +
      restHeight +
      Math.max(0, restCount - 1) * 8 +
      12,
    40, // minimum height
  );
}
