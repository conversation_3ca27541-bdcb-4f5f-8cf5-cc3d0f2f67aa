import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { isEmpty } from "braintrust/util";
import { useActiveCommentId, useActiveRowAndSpan } from "#/ui/query-parameters";
import { type RowId } from "#/utils/diffs/diff-objects";
import { useTransitionWithDelay } from "#/utils/use-transition-with-delay";
import { makeRowIdPrimaryOrigin } from "#/ui/table/use-active-row-effects";

export function useRowNavigation({ rowIds }: { rowIds: RowId[] }) {
  const [{ r: activeRowId }, setActiveRowAndSpan] = useActiveRowAndSpan();
  const [_, setFocusedCommentId] = useActiveCommentId();

  const rowIdx = isEmpty(activeRowId)
    ? -1
    : rowIds.findIndex(
        (r) =>
          makeRowIdPrimaryOrigin(r) === makeRowIdPrimaryOrigin(activeRowId),
      );

  const {
    isDelayedPending: isDelayedSpanChangeTransitioning,
    startTransition: startSpanChangeTransition,
  } = useTransitionWithDelay(100);

  const hasPrevRow = rowIdx > 0;
  const hasNextRow = rowIdx < rowIds.length - 1;

  const onPrevRow = useDebouncedCallback(
    async (opts?: { withTransition?: boolean }) => {
      if (document.getElementById("nested-dropdown-dataset")) {
        return;
      }
      const rowId = rowIds[rowIdx - 1] ?? null;
      if (opts?.withTransition) {
        startSpanChangeTransition(() => {
          if (hasPrevRow) {
            setActiveRowAndSpan({ r: rowId, s: null });
          }
          setFocusedCommentId(null);
        });
      } else {
        if (hasPrevRow) {
          await setActiveRowAndSpan({ r: rowId, s: null });
          await setFocusedCommentId(null);
        }
      }
    },
    10,
  );

  const onNextRow = useDebouncedCallback(
    async (opts?: { withTransition?: boolean }) => {
      if (document.getElementById("nested-dropdown-dataset")) {
        return;
      }
      const rowId = rowIds[rowIdx + 1] ?? null;
      if (opts?.withTransition) {
        startSpanChangeTransition(() => {
          if (hasNextRow) {
            setActiveRowAndSpan({ r: rowId, s: null });
          }
          setFocusedCommentId(null);
        });
      } else {
        if (hasNextRow) {
          await setActiveRowAndSpan({ r: rowId, s: null });
          await setFocusedCommentId(null);
        }
      }
    },
    10,
  );

  const jumpToRow = useDebouncedCallback((rowIndexToJumpTo: number) => {
    if (rowIndexToJumpTo >= 1 && rowIndexToJumpTo <= rowIds.length) {
      setActiveRowAndSpan({ r: rowIds[rowIndexToJumpTo - 1] ?? null, s: null });
    }
    setFocusedCommentId(null);
  }, 10);

  return {
    hasPrevRow,
    hasNextRow,
    onPrevRow,
    onNextRow,
    jumpToRow,
    isDelayedSpanChangeTransitioning,
    startSpanChangeTransition,
    rowIdx,
    activeRowId,
  };
}
