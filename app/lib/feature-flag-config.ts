import { MINIMUM_BTQL_API_VERSION } from "#/utils/btql/constants";
import z from "zod";

export const featureFlagsSchema = z
  .strictObject({
    enableAdvancedMetrics: z.boolean().optional().default(true),
    columnstoreSearch: z.boolean().optional().default(false),
    disableColumnstoreSearch: z.boolean().optional().default(false),
    disableRealtime: z.boolean().optional().default(false),
    functions: z.boolean().optional().default(true),
    views: z.boolean().optional().default(true),
    errorCount: z.boolean().optional().default(true),
    btqlMatchFilters: z.boolean().optional().default(false),
    btqlFastSearch: z.boolean().optional().default(false),
    customFunctions: z.boolean().optional().default(true),
    functionOrigin: z.boolean().optional().default(true),
    copilot: z.boolean().optional().default(true),
    copilotData: z.boolean().optional().default(true),
    copilotPrompts: z.boolean().optional().default(true),
    functionTools: z.boolean().optional().default(true),
    hasIsRootField: z.boolean().optional().default(true),
    customColumns: z.boolean().optional().default(true),
    brainstore: z.boolean().optional().default(false),
    brainstore_realtime: z.boolean().optional().default(true),
    brainstore_skip_backfill_check: z.boolean().optional().default(false),
    advancedSearch: z.boolean().optional().default(false),
    playX: z.boolean().optional().default(true),
    playXStop: z.boolean().optional().default(true),
    disableObjectCache: z.boolean().optional().default(isFirefox()),
    enableExpensiveSummaries: z.boolean().optional().default(false),
    queryDiagnostics: z.boolean().optional().default(false),
    fastExperimentSummary: z.boolean().optional().default(false),
    fastDatasetSummary: z.boolean().optional().default(false),
    schemaInference: z.boolean().optional().default(true),
    playxExtraMessages: z.boolean().optional().default(true),
    agents: z.boolean().optional().default(true),
    attachmentsInMessages: z.boolean().optional().default(true),
    remoteEvals: z.boolean().optional().default(true),
    flattenedBoolOps: z.boolean().optional().default(true),
    manyExperiments: z.boolean().optional().default(false),
    automations: z.boolean().optional().default(true),
    automationsExport: z.boolean().optional().default(true),
    thinking: z.boolean().optional().default(true),
    loop: z.boolean().optional().default(true),
    datasetAgentVar: z.boolean().optional().default(true),
    projectSummaryMetrics: z.boolean().optional().default(true),
    onlineScoringFilterAndTest: z.boolean().optional().default(true),
    summarySorting: z.boolean().optional().default(true),
    loadCustomColumnsFromScope: z.boolean().optional().default(true),
    loopTryOtherModels: z.boolean().optional().default(false),
    scorerTasks: z.boolean().optional().default(true),
    retention: z.boolean().optional().default(false),
    environments: z.boolean().optional().default(true),
    skellyLoop: z.boolean().optional().default(false),
    traceLevelMetrics: z.boolean().optional().default(false),
    fastInequalityFilters: z.boolean().optional().default(true),
    customColumnsV2: z.boolean().optional().default(true),
    customDashboard: z.boolean().optional().default(false),
    chartTextEditor: z.boolean().optional().default(false),
    retentionMoreObjects: z.boolean().optional().default(true),
  })
  .strip();
export type FeatureFlags = z.infer<typeof featureFlagsSchema>;

export const featureFlagConfig: Record<
  keyof FeatureFlags,
  {
    title: string;
    description?: string;
    minVersion: string;
    minDefaultVersion?: string;
    isHidden?: boolean;
    dependsOn?: keyof FeatureFlags;
    isDebugFlag?: boolean;
    onPremOnly?: boolean;
  }
> = {
  enableAdvancedMetrics: {
    title: "Additional summary metrics",
    description:
      "Display additional columns on the projects list table (eg. token count, ttf token, logs count, error count).",
    minVersion: MINIMUM_BTQL_API_VERSION,
    dependsOn: "brainstore",
    isDebugFlag: true,
  },
  enableExpensiveSummaries: {
    title: "Expensive summaries",
    description:
      "Enable expensive summary queries, even if you are running without a columnstore backend.",
    minVersion: MINIMUM_BTQL_API_VERSION,
    isDebugFlag: true,
    onPremOnly: true,
  },
  brainstore: {
    title: "Brainstore",
    minVersion: "0.0.62",
    description:
      "Next-gen data storage and query engine. Once enabled, Brainstore automatically powers search, analytics, and data loading in the UI.",
  },
  functions: {
    title: "Functions",
    minVersion: "0.0.50",
    isHidden: true,
  },
  views: {
    title: "Saved table views",
    minVersion: "0.0.49",
    isHidden: true,
  },
  errorCount: {
    title: "Error count",
    description: "Enable error count",
    minVersion: "0.0.52",
    isHidden: true,
  },
  customFunctions: {
    title: "Custom functions",
    minVersion: "0.0.53",
    isHidden: true,
  },
  functionOrigin: {
    title: "Function origin",
    minVersion: "0.0.54",
    isHidden: true,
  },
  copilot: {
    title: "Copilot",
    minVersion: "0.0.0",
    isHidden: true,
  },
  copilotData: {
    title: "Copilot data",
    minVersion: "0.0.0",
    isHidden: true,
  },
  copilotPrompts: {
    title: "Copilot prompts",
    minVersion: "0.0.0",
    isHidden: true,
  },
  functionTools: {
    title: "Function tools",
    minVersion: "0.0.56",
    isHidden: true,
  },
  hasIsRootField: {
    title: "Has the is_root field",
    minVersion: "0.0.57",
    isHidden: true,
  },
  customColumns: {
    title: "Custom table columns",
    minVersion: "0.0.60",
    isHidden: true,
  },
  btqlMatchFilters: {
    title: "Full-text-search index for BTQL match filters",
    description:
      "Enables an experimental full-text-search index for BTQL match filters. This should only be enabled when working with Braintrust support.",
    minVersion: "0.0.52",
    isDebugFlag: true,
  },
  btqlFastSearch: {
    title: "BTQL fast search",
    description: "Enables an experimental fast search mode for BTQL.",
    minVersion: "0.0.58",
    isDebugFlag: true,
  },
  columnstoreSearch: {
    title: "Force columnstore for log search",
    description:
      "Unlikely to improve performance, but can be useful when working with Braintrust support to test certain optimizations.",
    minVersion: MINIMUM_BTQL_API_VERSION,
    isDebugFlag: true,
  },
  disableColumnstoreSearch: {
    title: "Never use columnstore for log search",
    description:
      "By default, columnstore is dynamically enabled for log search. This flag disables columnstore for log search.",
    minVersion: MINIMUM_BTQL_API_VERSION,
    isDebugFlag: true,
  },
  disableRealtime: {
    title: "Disable real-time updates",
    description:
      "Disable real-time updates throughout the application. This should only be enabled for debugging purposes.",
    minVersion: "0.0.0",
    isDebugFlag: true,
  },
  brainstore_realtime: {
    title: "Brainstore realtime",
    minVersion: "0.0.60",
    description:
      "Enable realtime updates for Brainstore. You should only turn this off if debugging a performance issue with the Braintrust team.",
    dependsOn: "brainstore",
    isDebugFlag: true,
    isHidden: true,
  },
  brainstore_skip_backfill_check: {
    title: "Brainstore skip backfill check",
    minVersion: "0.0.60",
    description:
      "Skip checking if the data has been backfilled before using Brainstore. You should only turn this on if debugging a performance issue with the Braintrust team.",
    dependsOn: "brainstore",
    isDebugFlag: true,
    isHidden: true,
  },
  queryDiagnostics: {
    title: "Query diagnostics",
    minVersion: "0.0.60",
    description:
      "Include additional query diagnostics to help understand query performance.",
    dependsOn: "brainstore",
    isDebugFlag: true,
  },
  advancedSearch: {
    title: "Advanced search",
    minVersion: "0.0.60",
    isHidden: true,
  },
  playX: {
    title: "Full evals in playground",
    description:
      "Run full evaluations in the playground, with robust filtering, visualization and large dataset support.",
    minVersion: "0.0.63",
    isHidden: true,
  },
  playXStop: {
    title: "Stop playground evaluations server-side",
    description:
      "Aborts the evaluation server-side to prevent unnecessary model calls.",
    minVersion: "0.0.64",
    dependsOn: "playX",
    isHidden: true,
  },
  playxExtraMessages: {
    title: "Extra messages in playground",
    description:
      "Enable extra messages in the playground. This should only be enabled for debugging purposes.",
    minVersion: "0.0.65",
    isHidden: true,
  },
  fastExperimentSummary: {
    title: "Faster tables",
    description:
      "Load summary tables faster using some new optimizations. Currently will speed up the logs, experiments, and datasets views. Playgrounds soon!",
    minVersion: "0.0.65",
    minDefaultVersion: "0.0.73",
    isDebugFlag: true,
    dependsOn: "brainstore",
  },
  fastDatasetSummary: {
    title: "Faster tables for datasets",
    description:
      "Load dataset tables faster using fastExperimentSummary optimizations.",
    minVersion: "0.0.73",
    minDefaultVersion: "0.0.73",
    isDebugFlag: true,
    dependsOn: "brainstore",
    isHidden: true,
  },
  schemaInference: {
    title: "Schema inference",
    description: "Enable schema inference queries.",
    minVersion: "0.0.66",
    isHidden: true,
    dependsOn: "brainstore",
  },
  disableObjectCache: {
    title: "Disable object cache",
    description:
      "Disable the object cache (used to save experiments and playgrounds in your browser). This should only be enabled for debugging purposes.",
    minVersion: "0.0.0",
    isDebugFlag: true,
  },
  agents: {
    title: "Agents",
    description: "Prompt chaining in playgrounds.",
    minVersion: "0.0.66",
    dependsOn: "playX",
    isHidden: true,
  },
  attachmentsInMessages: {
    title: "Attachments in messages",
    minVersion: "0.0.68",
    isHidden: true,
  },
  remoteEvals: {
    title: "Run evals on remote servers",
    description: "Enable remote evals in the playground.",
    minVersion: "0.0.66",
    isHidden: true,
  },
  flattenedBoolOps: {
    title: "Flattened boolean ops",
    description: "Enable flattened boolean ops (makes BTQL parser faster).",
    minVersion: "0.0.69",
    isDebugFlag: true,
    isHidden: true,
  },
  manyExperiments: {
    title: "Fetch 2000 experiments (instead of 500) on the experiments page.",
    minVersion: MINIMUM_BTQL_API_VERSION,
    isDebugFlag: true,
  },
  automations: {
    title: "Automations",
    minVersion: "0.0.72",
    minDefaultVersion: "1.1.0",
    isHidden: true,
    dependsOn: "brainstore_realtime",
  },
  automationsExport: {
    title: "Automations export",
    minVersion: "0.0.75",
    minDefaultVersion: "1.1.0",
    isHidden: true,
    dependsOn: "automations",
  },
  thinking: {
    title: "Support AI Models with Reasoning Params & Thinking Tokens",
    minVersion: "0.0.74",
    isHidden: true,
  },
  loop: {
    title: "Loop",
    description: "AI optimize and generate prompts and data in playgrounds",
    minVersion: "0.0.74",
    isHidden: true,
  },
  datasetAgentVar: {
    title: "Dataset variable in agents",
    minVersion: "1.1.1",
    minDefaultVersion: "1.1.1",
    isHidden: true,
    dependsOn: "agents",
  },
  projectSummaryMetrics: {
    title: "Project summary metrics",
    description: "Display metrics in the project page.",
    minVersion: MINIMUM_BTQL_API_VERSION,
    isDebugFlag: true,
  },
  onlineScoringFilterAndTest: {
    title: "Online scoring filter and test",
    minVersion: "1.1.10",
    minDefaultVersion: "1.1.10",
    isHidden: true,
    dependsOn: "automations",
  },
  summarySorting: {
    title: "Sorting with summary shape",
    description: "Enable sorting in tables using summary shape.",
    minVersion: "1.1.12",
    isHidden: true,
    dependsOn: "brainstore",
  },
  loadCustomColumnsFromScope: {
    title: "Return custom columns in btql using custom_column_scope param",
    description:
      "Load custom columns using custom_column_scope param. This should only be enabled for debugging purposes.",
    minVersion: "1.1.12",
    isHidden: true,
    dependsOn: "brainstore",
  },
  loopTryOtherModels: {
    title: "Loop try other models",
    minVersion: "0.0.0",
    isHidden: true,
    dependsOn: "loop",
  },
  skellyLoop: {
    title: "Loop explorer",
    description: "Enable Loop explorer page.",
    minVersion: "1.1.17",
    isHidden: true,
    dependsOn: "loop",
  },
  scorerTasks: {
    title: "Scorer tasks",
    description: "Enable scorer tasks in the playground.",
    minVersion: "1.1.15",
    isHidden: true,
    dependsOn: "playX",
  },
  retention: {
    title: "Retention",
    description: "Time-based retention policies",
    minVersion: "1.1.19",
    minDefaultVersion: "1.1.20",
    isHidden: true,
    dependsOn: "automations",
  },
  environments: {
    title: "Environments",
    description: "Tag resources with environments",
    minVersion: "1.1.20",
    isHidden: true,
  },
  traceLevelMetrics: {
    title: "Trace level metrics",
    description: "Show trace level metrics on the monitoring page",
    minVersion: "1.1.19",
    isDebugFlag: true,
    dependsOn: "brainstore",
  },
  fastInequalityFilters: {
    title: "Fast inequality filters",
    description: "The version of brainstore can run fast inequality filters",
    minVersion: "1.1.20",
    isHidden: true,
    dependsOn: "brainstore",
  },
  customColumnsV2: {
    title:
      "Custom table columns V2 - relaxes the custom columns schema on the backend to allow adding custom columns to new tables without an API change",
    minVersion: "1.1.20",
    isHidden: true,
  },
  customDashboard: {
    title: "Custom monitor dashboards",
    description: "Customize charts on monitor page. Work in progress.",
    minVersion: "1.1.19",
    isHidden: true,
  },
  chartTextEditor: {
    title: "Chart text editor",
    description: "Show a text editor tab to edit custom chart schemas directly",
    minVersion: "1.1.19",
    isDebugFlag: true,
    dependsOn: "customDashboard",
  },
  retentionMoreObjects: {
    title: "Retention for experiments and datasets",
    description: "Enable retention for experiments and datasets",
    minVersion: "1.1.21",
    isHidden: true,
    dependsOn: "retention",
  },
};

// btql params depend on these flags
export type BTQLFeatureFlags = Pick<
  FeatureFlags,
  | "brainstore"
  | "brainstore_realtime"
  | "brainstore_skip_backfill_check"
  | "queryDiagnostics"
  | "customColumnsV2"
>;

function isFirefox(): boolean {
  if (typeof window === "undefined") return false;
  return navigator.userAgent.toLowerCase().includes("firefox");
}

export const FAST_OBJECT_TYPES = ["experiment", "project_logs"];
export const ORGS_WITHOUT_PROJECT_SUMMARY = ["Portola AI", "Perplexity AI"];
export const ORGS_WITHOUT_ADVANCED_METRICS = [
  "Netflix",
  "zapier.com",
  ...ORGS_WITHOUT_PROJECT_SUMMARY,
];
export const ORGS_WITHOUT_ENVIRONMENTS = ["stripe", "stripe-qa"];

// flags that we always make false until remote overrides are ready
export const NOT_READY_FALSE_FLAGS = new Set([
  "projectSummaryMetrics",
  "enableAdvancedMetrics",
]);
