import type Orb from "orb-billing";
import * as Sentry from "@sentry/nextjs";
import Link from "next/link";
import { useState } from "react";
import {
  ArrowDown,
  ChartNoAxesColumn,
  CreditCard,
  Receipt,
} from "lucide-react";
import { cn } from "#/utils/classnames";
import { But<PERSON>, buttonVariants } from "#/ui/button";
import { getPageName } from "#/utils/analytics";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "#/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "#/ui/select";
import TextArea from "#/ui/text-area";
import { Label } from "#/ui/label";
import { isEnterprisePlan, isFreePlan, isProPlan } from "./plans";
import { invokeServerAction } from "#/utils/invoke-server-action";
import { type downgradeProSubscription } from "#/utils/billing/utils";
import { useAuth } from "@clerk/nextjs";
import { useOrg } from "#/utils/user";
import { useRouter } from "next/navigation";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { Alert, AlertDescription } from "#/ui/alert";

function formatDate(date: string | null): string {
  if (!date) return "unknown";
  return new Date(date).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

function formatErrorMessage(error: unknown, action: string): string {
  const baseMessage = `Failed to ${action}.`;
  const supportMessage =
    "Please try again. Contact <NAME_EMAIL> if the problem persists.";

  if (error instanceof Error) {
    return `${baseMessage} ${error.message}. ${supportMessage}`;
  }

  return `${baseMessage} ${supportMessage}`;
}

function DowngradeModal({
  isOpen,
  onOpenChange,
}: {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}) {
  const { track } = useAppAnalytics();
  const router = useRouter();
  const org = useOrg();
  const { getToken } = useAuth();

  const [isDowngradeInProgress, setIsDowngradeInProgress] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [downgradeReason, setDowngradeReason] = useState<string>("");
  const [otherReason, setOtherReason] = useState<string>("");

  const handleDowngrade = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (
      !downgradeReason ||
      (downgradeReason === "other" && !otherReason.trim())
    ) {
      return;
    }

    setIsDowngradeInProgress(true);
    setError(null);

    if (org.id) {
      try {
        await invokeServerAction<typeof downgradeProSubscription>({
          fName: "downgradeProSubscription",
          args: {
            orgId: org.id,
          },
          getToken,
        });
        onOpenChange(false);
        track("downgradeProSubscription", {
          reason: downgradeReason,
          otherReason,
        });

        // Refresh the page to get the new subscription status
        router.refresh();
      } catch (error) {
        setError(formatErrorMessage(error, "downgrade subscription"));
        Sentry.captureException(error, {
          tags: {
            page: "billing-page",
          },
        });
      } finally {
        setIsDowngradeInProgress(false);
      }
    }
  };

  const handleClose = () => {
    setError(null);
    setDowngradeReason("");
    setOtherReason("");
    onOpenChange(false);
  };

  const isFormValid =
    downgradeReason && (downgradeReason !== "other" || otherReason.trim());

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Downgrade to free plan</DialogTitle>
          <DialogDescription>
            <span className="block pb-2">
              Are you sure you want to downgrade to the Free plan?
            </span>
            <span className="block">
              You&apos;ll be charged for any Pro plan usage this month, and your
              account will immediately switch to Free plan limits.
            </span>
          </DialogDescription>
        </DialogHeader>
        {error && (
          <div className="mb-4 rounded-md p-3 text-sm bg-red-50 text-red-700">
            {error}
          </div>
        )}
        <form onSubmit={handleDowngrade} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="downgrade-reason">Why are you downgrading?</Label>
            <Select
              value={downgradeReason || undefined}
              onValueChange={setDowngradeReason}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a reason" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="too-expensive">Too expensive</SelectItem>
                <SelectItem value="not-using-features">
                  Not using Pro features
                </SelectItem>
                <SelectItem value="switching-platform">
                  Switching to another platform
                </SelectItem>
                <SelectItem value="temporary">Temporary downgrade</SelectItem>
                <SelectItem value="budget-cuts">Budget cuts</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {downgradeReason === "other" && (
            <div className="space-y-2">
              <Label htmlFor="other-reason">Please specify</Label>
              <TextArea
                id="other-reason"
                placeholder="Tell us more about why you're downgrading..."
                value={otherReason}
                onChange={(e) => setOtherReason(e.target.value)}
                minRows={3}
                maxRows={6}
              />
            </div>
          )}

          <DialogFooter className="flex flex-col gap-2 sm:flex-row sm:gap-0">
            <Button type="button" variant="ghost" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={isDowngradeInProgress}
              variant="primary"
              disabled={!isFormValid}
            >
              Downgrade
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

function CurrentSubscriptionSummary({
  currentSubscription,
  paymentMethodSummary,
  invoices,
  billingReportingNotice,
}: {
  currentSubscription: Orb.Subscription;
  paymentMethodSummary: {
    last4: string;
    brand: string;
    exp_month: number;
    exp_year: number;
  } | null;
  invoices: Orb.Invoice[] | null;
  billingReportingNotice: string;
}) {
  const [showDowngradeModal, setShowDowngradeModal] = useState(false);
  const { track } = useAppAnalytics();
  const org = useOrg();

  const planId = currentSubscription.plan.id;
  const planName = currentSubscription.plan.name;
  const status = currentSubscription.status;
  const startDate = formatDate(currentSubscription.start_date);
  const renewsOn = formatDate(
    currentSubscription.current_billing_period_end_date,
  );

  let subscriptionStatusMessage = isFreePlan(planId)
    ? "This organization is currently on the free plan"
    : `This organization is currently on the ${planName} plan and the subscription is ${status}`;
  const isEnterprise = isEnterprisePlan(currentSubscription.plan);
  if (isEnterprise) {
    subscriptionStatusMessage = `This organization is currently on an Enterprise contract`;
  }

  const customerPortalUrl = currentSubscription.customer.portal_url;

  const isPro = isProPlan(planId);

  return (
    <div className="mb-12">
      <h2 className="mb-4 text-lg font-semibold">Current plan</h2>
      <div className="rounded-md border p-6">
        <p className="mb-1 text-base">{subscriptionStatusMessage}</p>
        {isPro && (
          <p className="mb-1 text-sm text-primary-600">
            This plan was activated on {startDate} and renews on {renewsOn}.
          </p>
        )}
        {paymentMethodSummary && (
          <p className="my-4 font-mono text-sm text-primary-600">
            {paymentMethodSummary.brand && (
              <span className="capitalize">{paymentMethodSummary.brand}</span>
            )}{" "}
            **** {paymentMethodSummary.last4}
            {paymentMethodSummary.exp_month &&
              paymentMethodSummary.exp_year && (
                <span className="ml-2">
                  Exp: {String(paymentMethodSummary.exp_month).padStart(2, "0")}
                  /{String(paymentMethodSummary.exp_year).slice(-2)}
                </span>
              )}
          </p>
        )}

        <div className="flex flex-col gap-2 sm:flex-row">
          {customerPortalUrl && (
            <Link
              target="_blank"
              href={customerPortalUrl}
              className={cn(
                buttonVariants({
                  size: "sm",
                }),
              )}
              onClick={() => {
                track("viewUsageClick", {
                  orgName: org?.name ?? "",
                  orgId: org?.id ?? "",
                  entryPoint: "billingPage",
                  destinationUrl: customerPortalUrl,
                  sourcePage: getPageName(),
                });
              }}
            >
              <ChartNoAxesColumn className="size-3" />
              View usage
            </Link>
          )}
          {invoices?.length && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  Icon={Receipt}
                  iconClassName="size-3"
                  isDropdown
                >
                  Invoices
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-80">
                <DropdownMenuLabel>Recent invoices</DropdownMenuLabel>
                {invoices.map((invoice) => (
                  <DropdownMenuItem key={invoice.id} className="p-0">
                    <Link
                      href={
                        invoice.hosted_invoice_url || customerPortalUrl || "#"
                      }
                      target="_blank"
                      className="flex w-full flex-col gap-1 p-2 text-xs"
                      onClick={() => {
                        track("viewInvoiceClick", {
                          orgName: org?.name ?? "",
                          orgId: org?.id ?? "",
                          entryPoint: "billingPage",
                          destinationUrl:
                            invoice.hosted_invoice_url ||
                            customerPortalUrl ||
                            "#",
                          invoiceId: invoice.id,
                          invoiceNumber:
                            invoice.invoice_number ||
                            `Invoice ${invoice.id.slice(-8)}`,
                          invoiceAmount: new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency:
                              invoice.currency === "credits"
                                ? "USD"
                                : invoice.currency,
                          }).format(parseFloat(invoice.amount_due)),
                          sourcePage: getPageName(),
                        });
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium">
                          {invoice.invoice_number ||
                            `Invoice ${invoice.id.slice(-8)}`}
                        </span>
                        <span className="tabular-nums">
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency:
                              invoice.currency === "credits"
                                ? "USD"
                                : invoice.currency,
                          }).format(parseFloat(invoice.amount_due))}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-primary-500">
                        <span>{formatDate(invoice.invoice_date)}</span>
                        <span className="capitalize">{invoice.status}</span>
                      </div>
                    </Link>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          {paymentMethodSummary && (
            <Link
              href="billing/payment"
              className={cn(buttonVariants({ size: "sm" }))}
            >
              <CreditCard className="size-3" />
              Update payment method
            </Link>
          )}
          {isPro && (
            <Button
              size="sm"
              Icon={ArrowDown}
              iconClassName="size-3"
              onClick={() => setShowDowngradeModal(true)}
            >
              Downgrade to free
            </Button>
          )}
        </div>

        {billingReportingNotice && (
          <Alert className="mt-6">
            <AlertDescription>
              <strong>Notice:</strong> {billingReportingNotice}
            </AlertDescription>
          </Alert>
        )}
      </div>
      <DowngradeModal
        isOpen={showDowngradeModal}
        onOpenChange={setShowDowngradeModal}
      />
    </div>
  );
}

export { CurrentSubscriptionSummary };
