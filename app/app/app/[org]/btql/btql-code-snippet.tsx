"use client";

import { useFeatureFlags } from "#/lib/feature-flags";
import { Button } from "#/ui/button";
import CodeToCopy from "#/ui/code-to-copy";
import { Dialog, DialogContent, DialogTitle, DialogTrigger } from "#/ui/dialog";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "#/ui/tabs";
import { BasicTooltip } from "#/ui/tooltip";
import { useOrg } from "#/utils/user";
import { _urljoin } from "braintrust/util";
import { Code } from "lucide-react";
import { useState } from "react";

export function CopyBtqlCodeSnippet({ value }: { value: string }) {
  const [open, setOpen] = useState(false);
  const { api_url } = useOrg();
  const {
    flags: { brainstore },
  } = useFeatureFlags();

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <div>
          <BasicTooltip tooltipContent="Get code snippet">
            <Button
              variant="ghost"
              size="icon"
              className="size-7 text-primary-500"
              Icon={Code}
            />
          </BasicTooltip>
        </div>
      </DialogTrigger>
      <DialogContent
        onOpenAutoFocus={(e) => {
          e.preventDefault();
        }}
        className="flex flex-col gap-4 overflow-auto sm:max-w-screen-lg"
      >
        <DialogTitle>Execute BTQL query</DialogTitle>

        <Tabs defaultValue="fetch" className="w-full">
          <TabsList className="h-auto rounded-md p-0.5">
            {/*<TabsTrigger value="typescript" className="rounded-[3px] text-xs">
              TypeScript SDK
            </TabsTrigger>*/}
            <TabsTrigger value="fetch" className="rounded-[3px] text-xs">
              TypeScript (fetch)
            </TabsTrigger>
            {/*<TabsTrigger value="python" className="rounded-[3px] text-xs">
              Python SDK
            </TabsTrigger>*/}
            <TabsTrigger value="requests" className="rounded-[3px] text-xs">
              Python (requests)
            </TabsTrigger>
            <TabsTrigger value="curl" className="rounded-[3px] text-xs">
              cURL
            </TabsTrigger>
          </TabsList>
          <TabsContent value="fetch">
            <CodeToCopy
              language={"typescript"}
              highlighterClassName="text-xs"
              data={`\
async function runQuery() {
  let cursor = null;
  while (true) {
    const response: Response = await fetch("${_urljoin(api_url, "btql")}", {
      method: "POST",
      body: JSON.stringify({
        query:
          \`${value}\` + (cursor ? \`\n| cursor: '\${cursor}'\` : ""),${
            brainstore
              ? `
        use_brainstore: true,
        brainstore_realtime: true, // Include the latest realtime data, but a bit slower.`
              : ""
          }
      }),
      headers: {
        Authorization: "Bearer " + process.env.BRAINTRUST_API_KEY, // Substitute your API key here
        "Content-Type": "application/json",
      },
    });
    if (!response.ok) {
      throw new Error(
        \`Failed to fetch: \${response.status} (\${response.statusText}) \${await response.text()}\`
      );
    }
    const { data, cursor: newCursor } = await response.json();

    for (const row of data) {
      console.log(row);
    }
    cursor = newCursor;

    if (data.length === 0) {
      break;
    }
  }
}

runQuery().catch(console.error);
`}
            />
          </TabsContent>
          {/* <TabsContent value="typescript"></TabsContent> */}
          {/* <TabsContent value="python"></TabsContent> */}
          <TabsContent value="requests">
            <CodeToCopy
              language={"python"}
              highlighterClassName="text-xs"
              data={`\
# /// script
# dependencies = [
#   "requests",
# ]
# ///
import os
import requests

def run_query():
    cursor = None
    while True:
        response = requests.post(
            "${_urljoin(api_url, "btql")}",
            json={
                "query": """${value}"""
                + (f" | cursor: '{cursor}'" if cursor else ""),${
                  brainstore
                    ? `
                "use_brainstore": True,
                "brainstore_realtime": True,  # Include the latest realtime data, but a bit slower.`
                    : ""
                }
            },
            headers={
                # Substitute your API key here
                "Authorization": "Bearer " + os.environ["BRAINTRUST_API_KEY"],
            },
        )
        response.raise_for_status()
        response_json = response.json()
        data = response_json.get("data", [])
        cursor = response_json.get("cursor")

        for row in data:
            print(row)

        if not data:
            break

run_query()`}
            />
          </TabsContent>
          <TabsContent value="curl">
            <CodeToCopy
              language={"bash"}
              highlighterClassName="text-xs"
              data={`\
#!/bin/bash

# This simple request fetches a single page.
# Use TypeScript or Python to perform pagination.
curl -X POST '${_urljoin(api_url, "btql")}' \
  -H "Authorization: Bearer $BRAINTRUST_API_KEY" \
  -H 'Content-Type: application/json' \
  --data @- << EOF
${JSON.stringify(
  {
    query: value,
    ...(brainstore
      ? {
          use_brainstore: true,
          brainstore_realtime: true,
        }
      : {}),
  },
  null,
  2,
)}
EOF`}
            />
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
