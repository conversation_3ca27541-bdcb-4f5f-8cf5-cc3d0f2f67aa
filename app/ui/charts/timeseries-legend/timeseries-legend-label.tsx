import { cn } from "#/utils/classnames";
import { ChartSymbol } from "#/ui/charts/symbols";
import { type TimeseriesVizType } from "#/ui/charts/timeseries/timeseries.types";
import { COLOR_CLASSNAMES, DEFAULT_COLOR_CLASSNAME } from "#/ui/charts/colors";

import { type TimeseriesLegendLabelData } from "./timeseries-legend.types";

export const TimeseriesLegendLabel = (
  props: TimeseriesLegendLabelData & {
    className?: string;
    contentClassName?: string;
    onClick?: React.MouseEventHandler;
    onMouseEnter?: React.MouseEventHandler;
    onMouseLeave?: React.MouseEventHandler;
    vizType: TimeseriesVizType;
  },
) => {
  const {
    className,
    contentClassName,
    label,
    onClick,
    onMouseEnter,
    onMouseLeave,
    formattedValue,
    vizType,
  } = props;
  const symbol = (
    <ChartSymbol
      className={cn(
        COLOR_CLASSNAMES[props.colorIndex] || DEFAULT_COLOR_CLASSNAME,
        className,
        contentClassName,
      )}
      // use square for bars and circles for lines
      index={vizType === "bars" ? 3 : 0}
      size={8}
    />
  );

  return (
    <div
      className={cn("flex items-center gap-1", className)}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {symbol}
      <div
        className={cn("flex-1 truncate text-xs", contentClassName)}
        title={label}
      >
        {label}
      </div>
      <div
        className={cn(
          "flex-none text-xs font-semibold tabular-nums text-primary-900",
          contentClassName,
        )}
      >
        {formattedValue}
      </div>
    </div>
  );
};
