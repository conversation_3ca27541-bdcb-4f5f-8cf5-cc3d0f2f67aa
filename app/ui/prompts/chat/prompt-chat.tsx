import { MessageBubble } from "#/ui/LLMView";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { type ChatCompletionMessage, useRunPrompt } from "./use-run-prompt";
import { produce } from "immer";
import { Button } from "#/ui/button";
import {
  ArrowUpRight,
  CircleArrowUp,
  CurlyBraces,
  Equal,
  MessageCircleMore,
  MessageSquare,
  MoreHorizontal,
  X,
} from "lucide-react";
import { useSyncedPrompts } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { atom, useAtomValue } from "jotai";
import useEvent from "react-use-event-hook";
import Text<PERSON><PERSON> from "#/ui/text-area";
import { CollapsibleSection } from "#/ui/collapsible-section";
import { DataTextEditor } from "#/ui/data-text-editor";
import { getObjValueByPath, isObject } from "braintrust/util";
import { cn } from "#/utils/classnames";
import { usePromptVariables } from "#/ui/prompts/function-editor/use-prompt-variables";
import { unfoldNestedFields } from "#/utils/queries/metadata";
import {
  ExtraMessageEditor,
  type ExtraMessagesFormValues,
  extraMessagesSchema,
} from "#/app/app/[org]/p/[project]/prompts/extra-messages";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "#/ui/form";
import { InfoBanner } from "#/ui/info-banner";
import { chatCompletionMessageParamSchema } from "@braintrust/typespecs";
import z from "zod";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";

type Props = {
  id: string;
  runData: RunData;
  setRunData: Dispatch<SetStateAction<RunData>>;
  extraMessagesPath: string | undefined;
  setExtraMessagesPath: Dispatch<SetStateAction<string | undefined>>;
};
export type RunData = {
  input?: unknown;
  expected?: unknown;
  metadata?: Record<string, unknown>;
};

export function PromptChat({
  id,
  runData,
  setRunData,
  extraMessagesPath,
  setExtraMessagesPath,
}: Props) {
  const { projectId } = useContext(ProjectContext);

  const { sortedSyncedPromptsAtom_ROOT, addMessage } = useSyncedPrompts();
  const promptData = useAtomValue(
    useMemo(
      () => atom((get) => get(sortedSyncedPromptsAtom_ROOT)[0].prompt_data),
      [sortedSyncedPromptsAtom_ROOT],
    ),
  );

  const [messages, setMessages] = useState<ChatCompletionMessage[]>([]);
  const [toolDefinitions, setToolDefinitions] = useState<Map<string, string>>(
    new Map(),
  );
  const [input, setInput] = useState("");
  const [shouldAutoClear, setShouldAutoClear] = useState(false);
  const [variablesPanelOpen, setVariablesPanelOpen] = useState(false);
  const [scrollFromBottom, setScrollFromBottom] = useState<number | null>(null);

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const extraMessagesParsed = useMemo(() => {
    if (!extraMessagesPath) return { messages: undefined, error: undefined };
    const value = getObjValueByPath(runData, extraMessagesPath.split("."));
    const messages = z.array(chatCompletionMessageParamSchema).safeParse(value);
    return { messages: messages.data, error: messages.error };
  }, [runData, extraMessagesPath]);

  const onStreamChunk = useEvent((text: string) => {
    messagesContainerRef.current?.scrollTo({
      top: messagesContainerRef.current?.scrollHeight,
      behavior: "smooth",
    });
    setMessages(
      produce(messages, (draft) => {
        const lastMessage = draft[draft.length - 1];
        if (lastMessage?.role === "assistant") {
          lastMessage.content = text;
        } else {
          draft.push({
            content: text,
            role: "assistant" as const,
          });
        }
      }),
    );
  });

  const onStreamDone = useEvent((newMessages: ChatCompletionMessage[]) => {
    setMessages(
      produce(messages, (draft) => {
        const lastMessage = draft[draft.length - 1];
        if (lastMessage?.role === "assistant") {
          draft.splice(draft.length - 1, 1, ...newMessages);
        } else {
          draft.push(...newMessages);
        }
      }),
    );
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  });

  const onError = useCallback(() => {
    setMessages((prev) => {
      // Remove the loading assistant message
      return prev.filter((m) => !(m.role === "assistant" && m.content === ""));
    });
  }, []);

  const { runPrompt, isGenerating } = useRunPrompt({
    promptData,
    runData,
    onStreamChunk,
    onStreamDone,
    onError,
    setToolDefinitions,
    extraMessages: extraMessagesParsed.messages,
    parent: useMemo(
      () =>
        projectId
          ? {
              object_type: "project_logs",
              object_id: projectId,
            }
          : undefined,
      [projectId],
    ),
  });

  const { isMissingPromptVariables, addPromptVariables } = usePromptVariables({
    promptContent: promptData?.prompt,
    runData,
    setRunData,
    shouldPrependInput: true,
  });

  // Restore scroll position when variables panel toggles
  useEffect(() => {
    if (scrollFromBottom !== null && messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      // Wait for layout to settle
      setTimeout(() => {
        const newScrollTop =
          container.scrollHeight - container.clientHeight - scrollFromBottom;
        container.scrollTop = Math.max(0, newScrollTop);
        setScrollFromBottom(null);
      }, 0);
    }
  }, [variablesPanelOpen, scrollFromBottom]);

  const onSubmit = () => {
    const newMessages = shouldAutoClear ? [] : [...messages];
    // Allow submitting without a user message
    if (input.trim().length > 0) {
      newMessages.push({
        content: input,
        role: "user" as const,
      });
    }

    // Don't include the loading assistant message in the submitted prompt
    runPrompt(newMessages);

    newMessages.push({
      content: "",
      role: "assistant" as const,
    });

    setMessages(newMessages);
    setInput("");

    // Delay scroll so that new messages are added first
    setTimeout(() => {
      messagesContainerRef.current?.scrollTo({
        top: messagesContainerRef.current?.scrollHeight,
        behavior: "smooth",
      });
    }, 100);
  };

  return (
    <div className="flex min-h-0 flex-1 flex-col items-center">
      <div
        className="flex size-full flex-auto flex-col items-center gap-1 overflow-y-auto px-3 py-4"
        ref={messagesContainerRef}
      >
        <div className="w-full max-w-3xl">
          {extraMessagesParsed.messages &&
            extraMessagesParsed.messages.map((message, index) => (
              <MessageBubble
                key={index}
                message={message}
                toolDefinitions={toolDefinitions}
                extraMessagesPath={extraMessagesPath}
              />
            ))}
          {messages.map((message, index) => (
            <MessageBubble
              key={index}
              message={message}
              toolDefinitions={toolDefinitions}
              isLoading={
                isGenerating &&
                message.role === "assistant" &&
                message.content === ""
              }
              onDelete={() => {
                setMessages(
                  produce(messages, (draft) => {
                    draft.splice(index, 1);
                  }),
                );
              }}
              onAdd={() => {
                addMessage({ id, message });
              }}
            />
          ))}
        </div>
        {messages.length === 0 && (
          <div className="flex size-full flex-col items-center justify-center gap-4 text-primary-400">
            <MessageCircleMore className="size-8" />
            Start chatting by typing a message below
          </div>
        )}
      </div>
      <div className="flex w-full max-w-4xl flex-1 justify-center @container">
        <div
          className={cn(
            "flex w-full flex-col overflow-y-auto border-t bg-background placeholder:text-primary-500/90 @4xl:rounded-t-md @4xl:border dark:bg-primary-50",
            !variablesPanelOpen && "max-h-48",
          )}
        >
          <TextArea
            className="flex min-h-9 resize-none rounded-none border-0 px-3 bg-background focus-visible:ring-0 dark:bg-primary-50"
            value={input}
            minRows={1}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Chat with your prompt..."
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey && !isGenerating) {
                e.preventDefault();
                onSubmit();
              }
            }}
            ref={inputRef}
            disabled={isGenerating}
          />
          <div className="flex items-center justify-between pb-2 pl-1.5 pr-3">
            <div className="flex items-center">
              <Button
                size="xs"
                variant="ghost"
                isDropdown={!variablesPanelOpen}
                onClick={() => {
                  const container = messagesContainerRef.current;
                  // Store scroll position before toggling
                  if (container) {
                    const currentScrollFromBottom =
                      container.scrollHeight -
                      container.scrollTop -
                      container.clientHeight;
                    setScrollFromBottom(currentScrollFromBottom);
                  }

                  setVariablesPanelOpen((prev) => !prev);
                  if (isMissingPromptVariables) {
                    addPromptVariables();
                  }

                  if (variablesPanelOpen) {
                    inputRef.current?.focus();
                  }
                }}
                className={cn("text-primary-500 mr-1")}
              >
                {variablesPanelOpen ? "Hide variables" : "Variables"}
              </Button>
              {messages.length > 0 && !shouldAutoClear && (
                <Button
                  size="xs"
                  variant="ghost"
                  onClick={() => {
                    setMessages([]);
                    inputRef.current?.focus();
                  }}
                  className="text-primary-500"
                  Icon={X}
                >
                  Clear chat
                </Button>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size="xs" variant="ghost" Icon={MoreHorizontal} />
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuCheckboxItem
                    checked={shouldAutoClear}
                    onSelect={() => {
                      setShouldAutoClear((prev) => !prev);
                      inputRef.current?.focus();
                    }}
                  >
                    Auto-clear chat on each run
                  </DropdownMenuCheckboxItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="size-7"
              onClick={onSubmit}
              disabled={isGenerating}
              Icon={CircleArrowUp}
              iconClassName="size-4"
            />
          </div>
          {variablesPanelOpen && (
            <VariablesPanel
              setRunData={setRunData}
              runData={runData}
              id={id}
              extraMessagesPath={extraMessagesPath}
              setExtraMessagesPath={setExtraMessagesPath}
            />
          )}
        </div>
      </div>
    </div>
  );
}

function validateMetadataValue(v: unknown) {
  if (v != null && !isObject(v)) {
    throw new Error("Metadata must be an object");
  }
}

const ALLOWED_RENDER_OPTIONS_STRUCTURED = ["json" as const, "yaml" as const];
const ALLOWED_RENDER_OPTIONS_UNKNOWN = [
  ...ALLOWED_RENDER_OPTIONS_STRUCTURED,
  "text" as const,
];

function VariablesPanel({
  id,
  setRunData,
  runData,
  extraMessagesPath,
  setExtraMessagesPath,
}: {
  setRunData: Dispatch<SetStateAction<RunData>>;
  runData: RunData;
  id: string;
  extraMessagesPath: string | undefined;
  setExtraMessagesPath: (extraMessagesPath: string | undefined) => void;
}) {
  const form = useForm<ExtraMessagesFormValues>({
    resolver: zodResolver(extraMessagesSchema),
    defaultValues: {
      expression: extraMessagesPath,
    },
  });

  const expr = form.watch("expression");
  const hasErrors = Object.keys(form.formState.errors).length > 0;
  useEffect(() => {
    if (hasErrors) {
      return;
    }

    // Debounce state update to prevent infinite re-renders when typing quickly
    const timeoutId = setTimeout(() => {
      setExtraMessagesPath(expr);
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [hasErrors, setExtraMessagesPath, expr]);

  const datasetPaths = useMemo(() => {
    const inferredPaths = unfoldNestedFields([], runData, false).map((path) =>
      path.join("."),
    );
    // Ensure that inferred paths tab is always shown with the top level fields
    return Array.from(
      new Set([...inferredPaths, "metadata", "expected", "input"]),
    );
  }, [runData]);

  return (
    <div className="flex max-h-[calc(100vh-228px)] flex-col gap-2 overflow-y-scroll p-3 pt-0">
      <div>
        <CollapsibleSection
          title="Input"
          Icon={ArrowUpRight}
          localStorageKey={`${id}-runDataInput`}
        >
          <DataTextEditor
            value={runData.input ?? ""}
            onChange={(v) =>
              setRunData(
                produce(runData, (draft) => {
                  draft.input = v;
                }),
              )
            }
            autoFocus
            formatOnBlur
            allowedRenderOptions={ALLOWED_RENDER_OPTIONS_UNKNOWN}
          />
        </CollapsibleSection>
      </div>
      <div>
        <CollapsibleSection
          title="Expected"
          Icon={Equal}
          localStorageKey={`${id}-runDataExpected`}
        >
          <DataTextEditor
            value={runData.expected ?? ""}
            onChange={(v) =>
              setRunData(
                produce(runData, (draft) => {
                  draft.expected = v;
                }),
              )
            }
            formatOnBlur
            allowedRenderOptions={ALLOWED_RENDER_OPTIONS_UNKNOWN}
          />
        </CollapsibleSection>
      </div>
      <div>
        <CollapsibleSection
          title="Metadata"
          Icon={CurlyBraces}
          localStorageKey={`${id}-runDataMetadata`}
        >
          <DataTextEditor
            value={runData.metadata}
            onChange={(v) =>
              setRunData(
                produce(runData, (draft) => {
                  if (!isObject(v)) {
                    return;
                  }

                  draft.metadata = v;
                }),
              )
            }
            formatOnBlur
            validateFn={validateMetadataValue}
            allowedRenderOptions={ALLOWED_RENDER_OPTIONS_STRUCTURED}
          />
        </CollapsibleSection>
      </div>
      <div>
        <CollapsibleSection
          title="Extra messages"
          Icon={MessageSquare}
          localStorageKey={`${id}-extraMessages`}
          defaultCollapsed
        >
          <Form {...form}>
            <div className="rounded-md border px-2 bg-primary-50">
              <InfoBanner>
                Define a path from variables to messages. If an array of
                messages is found, they will be appended to the prompt.
              </InfoBanner>
              <ExtraMessageEditor
                currentExpression={extraMessagesPath}
                form={form}
                datasetPaths={datasetPaths}
              />
            </div>
          </Form>
        </CollapsibleSection>
      </div>
    </div>
  );
}
