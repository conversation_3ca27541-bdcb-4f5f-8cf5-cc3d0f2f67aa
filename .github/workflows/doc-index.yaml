name: Update Documentation Index

on:
  push:
    branches:
      - main
    paths:
      - "app/content/docs/**"
      - "app/content/blog/**"
  workflow_dispatch:
    inputs:
      namespace:
        description: "Turbopuffer namespace for indexing"
        required: false
        default: "braintrust-docs"
        type: string

permissions:
  contents: read

jobs:
  update-doc-index:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "22"

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest

      - name: Install dependencies
        run: pnpm install

      - name: Build workspace packages
        run: |
          pnpm build --filter @braintrust/core
          pnpm build --filter @braintrust/typespecs
          pnpm build --filter braintrust

      - name: Build doc-index CLI
        working-directory: local/js
        run: pnpm build

      - name: Generate documentation chunks
        working-directory: local/js
        run: npx doc-index chunk

      - name: Run doc index CLI
        working-directory: local/js
        run: npx doc-index index --namespace ${{ inputs.namespace || 'braintrust-docs' }}
        env:
          TURBOPUFFER_API_KEY: ${{ secrets.TURBOPUFFER_API_KEY }}
          TURBOPUFFER_REGION: ${{ secrets.TURBOPUFFER_REGION }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          NODE_ENV: production
