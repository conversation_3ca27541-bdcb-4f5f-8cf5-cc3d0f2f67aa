use clap::Parser;
use rand::{rngs::ThreadRng, Rng};
use serde::{Deserialize, Serialize};
use serde_json::{json, Map};

use util::{
    anyhow::anyhow,
    system_types::{make_object_schema, FullObjectIdOwned},
    xact::TransactionId,
    Result,
};

use crate::{
    config_with_store::ConfigWithStore,
    directory::cached_directory::FileCacheOpts,
    index_document::make_full_schema,
    merge::{
        merge_tantivy_segments, MergeOpts, MergeTantivySegmentsInput, MergeTantivySegmentsOptions,
    },
    optimize_tantivy_index::OptimizeObjectTuningOptions,
    process_wal::{
        compact_segment_wal, process_object_wal, CompactSegmentWalInput, CompactSegmentWalOptions,
        ProcessObjectWalInput, ProcessObjectWalOptions,
    },
    wal::wal_insert_unmerged,
    wal_entry::WalEntry,
};

pub struct StorageBenchInput {
    pub config: ConfigWithStore,
    pub file_cache_opts: FileCacheOpts,
}

#[derive(Parse<PERSON>, Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct StorageBenchOpts {
    #[arg(short, long, default_value_t = 100, env = "BRAINSTORE_BENCH_NUM_DOCS")]
    pub num_docs: usize,

    #[arg(
        short,
        long,
        default_value_t = 1000,
        env = "BRAINSTORE_BENCH_BATCH_SIZE"
    )]
    pub batch_size: usize,

    #[arg(long, env = "BRAINSTORE_BENCH_AVG_DOC_SIZE", value_parser = util::ByteSize::parse_to_usize, default_value_t = default_avg_doc_size(), help=format!("The maximum size of a file in the cache (defaults to {})", util::ByteSize::from(default_avg_doc_size())))]
    pub avg_doc_size: usize,

    #[arg(long, default_value_t = false, env = "BRAINSTORE_BENCH_DRY_RUN")]
    pub dry_run: bool,

    #[arg(long, default_value_t = false, env = "BRAINSTORE_BENCH_USE_MMAP")]
    pub use_mmap: bool,

    #[command(flatten)]
    pub process_wal_opts: ProcessObjectWalOptions,

    #[command(flatten)]
    #[serde(flatten)]
    pub compact_wal_opts: CompactSegmentWalOptions,

    #[command(flatten)]
    #[serde(flatten)]
    pub optimize_opts: OptimizeObjectTuningOptions,
}

fn default_avg_doc_size() -> usize {
    100 * 1024 // 100kb
}

fn make_base64_string(rng: &mut ThreadRng, len: usize) -> String {
    // Base64 characters: A-Z, a-z, 0-9, +, /
    const BASE64_CHARS: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    let mut ret = String::with_capacity(len);
    for _ in 0..len {
        let idx = rng.gen_range(0..BASE64_CHARS.len());
        ret.push(BASE64_CHARS[idx] as char);
    }
    ret
}

lazy_static::lazy_static! {
    static ref OBJECT_ID: FullObjectIdOwned = FullObjectIdOwned::default();
}

pub async fn stress_test_large_docs_insert(
    input: &StorageBenchInput,
    opts: &StorageBenchOpts,
) -> Result<()> {
    let mut rng = rand::thread_rng();
    let avg_doc_size = opts.avg_doc_size;
    let num_docs = opts.num_docs;
    let batch_size = opts.batch_size.min(num_docs);

    let mut batch_num = 0u64;
    let num_batches = (num_docs - 1) / batch_size + 1;

    // Keep track of all background tasks so we can await them at the end.
    let mut tasks: Vec<tokio::task::JoinHandle<Result<()>>> = Vec::new();

    let mut docs = Vec::new();
    for i in 0..num_docs {
        let range = avg_doc_size / 2;
        let doc_size = rng.gen_range(avg_doc_size - range..avg_doc_size + range);

        // Split the document size between input (50%) and metadata (50%)
        let input_size = (doc_size as f64 * 0.5) as usize;
        let metadata_size = doc_size - input_size;

        // Create input as an array of base64 strings, each max 100kb
        let max_chunk_size = 100 * 1024; // 100kb
        let mut input_array = Vec::new();
        let mut remaining_input_size = input_size;

        while remaining_input_size > 0 {
            let chunk_size = if remaining_input_size <= max_chunk_size {
                remaining_input_size
            } else {
                rng.gen_range(1..=std::cmp::min(remaining_input_size, max_chunk_size))
            };
            input_array.push(json!(make_base64_string(&mut rng, chunk_size)));
            remaining_input_size -= chunk_size;
        }

        // Create metadata as a sparse document with many small strings (10 bytes to 1kb)
        let mut metadata_map = Map::new();
        let mut remaining_metadata_size = metadata_size;
        let mut key_counter = 0;

        while remaining_metadata_size > 0 {
            // Account for key overhead (field_X format)
            let key = format!("field_{}", key_counter);
            let key_len = key.len();

            // If we don't have enough space for even the smallest value (10 bytes) plus the key, stop
            if remaining_metadata_size <= key_len + 10 {
                break;
            }

            let available_for_value = remaining_metadata_size - key_len;
            let max_value_size = std::cmp::min(1024, available_for_value);
            let value_size = if max_value_size <= 10 {
                max_value_size
            } else {
                rng.gen_range(10..=max_value_size)
            };

            metadata_map.insert(key, json!(make_base64_string(&mut rng, value_size)));
            remaining_metadata_size = remaining_metadata_size.saturating_sub(value_size + key_len);
            key_counter += 1;
        }

        let data = Map::from_iter(
            [
                ("input".to_string(), json!(input_array)),
                // Use expected instead of metadata so we don't waste time on the columnstore
                ("expected".to_string(), json!(metadata_map)),
            ]
            .into_iter(),
        );

        docs.push(WalEntry {
            id: format!("{}", i),
            _xact_id: TransactionId(batch_num),
            data,
            _object_id: OBJECT_ID.object_id.clone(),
            _object_type: OBJECT_ID.object_type.clone(),
            ..Default::default()
        });

        if docs.len() >= batch_size {
            batch_num += 1;
            let released_batch = std::mem::take(&mut docs);
            eprintln!("Writing batch {}/{}", batch_num, num_batches);

            let config_clone = input.config.clone();
            let opts_clone = opts.clone();
            let batch_num_clone = batch_num;

            let handle = tokio::spawn(async move {
                let start = std::time::Instant::now();
                let res = write_and_index_wal_entries(
                    config_clone,
                    opts_clone,
                    batch_num_clone,
                    released_batch,
                )
                .await;
                if let Ok(_) = &res {
                    eprintln!(
                        "-- Wrote batch {} in {:?}",
                        batch_num_clone,
                        start.elapsed()
                    );
                }
                res
            });
            tasks.push(handle);
        }

        if tasks.len() >= 10 {
            eprintln!("Waiting for {} tasks to finish", tasks.len());
            for task in tasks.drain(..) {
                task.await??;
            }
        }
    }

    // Process any remaining docs that didn't fill an entire batch.
    if !docs.is_empty() {
        let config_clone = input.config.clone();
        let opts_clone = opts.clone();
        let batch_num_clone = batch_num;
        let handle = tokio::spawn(async move {
            let start = std::time::Instant::now();
            let res =
                write_and_index_wal_entries(config_clone, opts_clone, batch_num_clone, docs).await;
            if let Ok(_) = &res {
                eprintln!(
                    "-- Wrote batch {} in {:?}",
                    batch_num_clone,
                    start.elapsed()
                );
            }
            res
        });
        tasks.push(handle);
    }

    // Wait for all background processing/compaction tasks to finish.
    for (idx, task) in tasks.into_iter().enumerate() {
        match task.await {
            Ok(res) => res?,
            Err(e) => return Err(anyhow!("background task {} panicked: {:?}", idx, e)),
        }
    }

    eprintln!("DONE!");
    Ok(())
}

async fn write_and_index_wal_entries(
    config: ConfigWithStore,
    opts: StorageBenchOpts,
    batch_num: u64,
    wal_entries: Vec<WalEntry>,
) -> Result<()> {
    wal_insert_unmerged(
        config.wal.as_ref(),
        config.global_store.as_ref(),
        wal_entries,
    )
    .await?;
    let start = std::time::Instant::now();
    eprintln!("Processing batch {}", batch_num);
    let output = process_object_wal(
        ProcessObjectWalInput {
            object_id: OBJECT_ID.as_ref(),
            config: &config,
        },
        Default::default(),
        opts.process_wal_opts.clone(),
    )
    .await?;
    eprintln!("-- Processed batch {} in {:?}", batch_num, start.elapsed());

    let segments = output.modified_segment_ids;

    eprintln!(
        "Compacting batch {} ({} segment{})",
        batch_num,
        segments.len(),
        if segments.len() > 1 { "s" } else { "" }
    );
    for segment_id in segments {
        eprintln!("Compacting batch {} segment {}", batch_num, segment_id);
        let start = std::time::Instant::now();
        compact_segment_wal(
            CompactSegmentWalInput {
                segment_id,
                index_store: config.index.clone(),
                schema: make_full_schema(&make_object_schema(OBJECT_ID.object_type)?)?,
                global_store: config.global_store.clone(),
                locks_manager: config.locks_manager.clone(),
            },
            Default::default(),
            opts.compact_wal_opts.clone(),
        )
        .await?;
        eprintln!(
            "-- Compacted batch {} segment {} in {:?}",
            batch_num,
            segment_id,
            start.elapsed()
        );
    }

    Ok(())
}

pub async fn stress_test_large_docs_merge(
    input: &StorageBenchInput,
    opts: &StorageBenchOpts,
) -> Result<()> {
    let segments = input
        .config
        .global_store
        .list_segment_ids_global(None)
        .await?;

    eprintln!(
        "Merging {} segment{}",
        segments.len(),
        if segments.len() != 1 { "s" } else { "" }
    );
    for segment in segments {
        eprintln!("Merging segment {}", segment);
        let segment_metadata = input
            .config
            .global_store
            .query_segment_metadatas(&[segment])
            .await
            .unwrap()
            .remove(0);
        eprintln!(
            "Num chunks: {:?}",
            segment_metadata
                .last_compacted_index_meta
                .ok_or_else(|| anyhow!("No last compacted index meta"))?
                .tantivy_meta
                .segments
                .len()
        );

        let start = std::time::Instant::now();

        let config = input.config.with_new_indexing_directory(FileCacheOpts {
            page_size: opts.optimize_opts.merge_page_size,
            memory_limit: opts.optimize_opts.merge_memory_limit,
            use_mmap_directory: true,
            ..input.file_cache_opts.clone()
        })?;

        merge_tantivy_segments(
            MergeTantivySegmentsInput {
                segment_id: segment,
                config,
                schema: make_full_schema(&make_object_schema(OBJECT_ID.object_type)?)?,
                dry_run: opts.dry_run,
                try_acquire: false,
            },
            Default::default(),
            MergeTantivySegmentsOptions {
                merge_opts: MergeOpts {
                    target_num_segments: 1,
                    use_exact_num_merge_policy: true,
                    ..Default::default()
                },
                ..Default::default()
            },
        )
        .await?;
        eprintln!("-- Merged in {:?}", start.elapsed());
    }

    Ok(())
}
