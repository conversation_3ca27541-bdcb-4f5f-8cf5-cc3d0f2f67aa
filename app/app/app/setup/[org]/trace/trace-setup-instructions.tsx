"use client";
import { useState, useEffect, useRef, useMemo } from "react";
import { motion } from "motion/react";
import { But<PERSON> } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "#/ui/tabs";
import CodeToCopy from "#/ui/code-to-copy";
import { ArrowUpRight } from "lucide-react";
import Link from "next/link";
import { buttonVariants } from "#/ui/button";
import { getProviderConfig } from "../../../[org]/settings/secrets/utils";
import {
  getCurrentCodeExample,
  Language,
  SdkIntegration,
  getIntegrationConfigs,
  VercelFramework,
} from "./trace-config";
import { PythonLogo } from "../../../[org]/onboarding-logos";
import { TypescriptLogo } from "../../../[org]/onboarding-logos";

import { useUser } from "#/utils/user";
import { Spinner } from "#/ui/icons/spinner";
import { useApiKeyGeneration } from "#/utils/use-api-key-generation";
import ModuleInstaller from "#/ui/docs/module-installer";

const getLanguageIcon = (name: string, size = 24) => {
  if (name.toLowerCase().startsWith("python")) {
    return <PythonLogo size={size} />;
  }
  if (name.toLowerCase().startsWith("typescript")) {
    return <TypescriptLogo size={size} />;
  }
};

const isProviderLangDisabled = (
  provider: SdkIntegration,
  language: Language,
): boolean => {
  const providerConfig = getIntegrationConfigs()[provider];
  return !providerConfig?.codeExamples?.[language];
};

interface TraceSetupInstructionsProps {
  orgName: string;
  apiKey?: string;
  projectId?: string;
  projectName?: string;
  showKeyGeneration?: boolean;
}

export function TraceSetupInstructions({
  orgName,
  apiKey,
  projectId,
  projectName,
  showKeyGeneration = true,
}: TraceSetupInstructionsProps) {
  const [llmProvider, setLlmProvider] = useState<SdkIntegration>(
    Object.values(SdkIntegration)[0],
  );
  const [lang, setLang] = useState<Language>(Object.values(Language)[0]);
  const [framework, setFramework] = useState<VercelFramework>(
    VercelFramework.NEXT,
  );

  const integrationsSectionRef = useRef<HTMLDivElement | null>(null);
  const instructionsContainerRef = useRef<HTMLDivElement | null>(null);

  const { orgs } = useUser();
  const org = orgs[orgName];

  const { generatedApiKey, creating, createKey } = useApiKeyGeneration({
    keyNamePrefix: "logs-setup-generated",
  });

  const providerConfigs = getIntegrationConfigs(
    apiKey ?? generatedApiKey ?? undefined,
    org.api_url,
    projectId,
    projectName,
  );
  const providers = useMemo(() => Object.values(SdkIntegration), []);
  const selectedProviderConfig = providerConfigs[llmProvider];
  const codeExample = getCurrentCodeExample(
    llmProvider,
    lang,
    framework,
    projectName,
  );
  const providerCodeExamples = codeExample?.snippets ?? [];

  useEffect(() => {
    if (isProviderLangDisabled(llmProvider, lang)) {
      setLang(selectedProviderConfig.supportedLanguages[0]);
    }
  }, [lang, llmProvider, selectedProviderConfig.supportedLanguages]);

  return (
    <div className="w-full max-w-[38rem]">
      <motion.div
        key="providers-and-frameworks"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{
          opacity: {
            duration: 0.3,
            ease: "easeOut",
          },
        }}
        className="text-sm"
      >
        Providers and frameworks
      </motion.div>
      <div
        ref={integrationsSectionRef}
        className="group relative mb-8 w-full scroll-mt-8"
      >
        <div className="grid grid-cols-4 gap-2 py-2 md:grid-cols-5">
          {providers.map((provider, index) => {
            const providerConfig = providerConfigs[provider];
            const isSelected = llmProvider === provider;
            const { Icon } = getProviderConfig(provider);
            return (
              <motion.div
                key={provider}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                whileHover={{ y: 2 }}
                transition={{
                  opacity: {
                    duration: 0.2,
                    ease: "easeOut",
                    delay: (index + 1) * 0.02,
                  },
                  y: {
                    duration: 0.2,
                    ease: "easeOut",
                    delay: 0,
                  },
                }}
                className="flex w-full flex-none"
              >
                <Button
                  className={cn(
                    "flex-1 px-0 text-xs flex flex-col items-center snap-center border",
                    { "border-primary-500 bg-background": isSelected },
                  )}
                  onClick={() => {
                    setLlmProvider(provider);
                    integrationsSectionRef.current?.scrollIntoView({
                      behavior: "smooth",
                      block: "start",
                    });
                  }}
                >
                  {providerConfig.icon ?? <Icon size={36} />}
                  <span className="text-xs">
                    {providerConfig?.name ?? provider}
                  </span>
                </Button>
              </motion.div>
            );
          })}
        </div>
      </div>
      <div className="-mx-6">
        <motion.div
          key="instructions"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{
            opacity: {
              duration: 0.4,
              ease: "easeOut",
              delay: (providers.length + 4) * 0.02,
            },
          }}
          ref={instructionsContainerRef}
          className="group relative scroll-mt-2"
        >
          <div className="relative w-full rounded-lg border p-6 bg-background border-primary-100">
            <Tabs value={lang} className="mb-8 flex w-full flex-col">
              <TabsList className="inline-flex h-auto self-start p-1">
                {Object.values(Language).map((language) => {
                  const isDisabled = isProviderLangDisabled(
                    llmProvider,
                    language,
                  );
                  if (!isDisabled) {
                    return (
                      <TabsTrigger key={language} asChild value={language}>
                        <Button
                          size="xs"
                          className="rounded border-0 text-xs text-primary-500"
                          onClick={() => setLang(language)}
                        >
                          {getLanguageIcon(language, 16)} {language}
                        </Button>
                      </TabsTrigger>
                    );
                  }
                  return null;
                })}
              </TabsList>
            </Tabs>
            <div className="flex flex-col gap-6">
              <div className="mb-2 flex flex-col gap-3">
                <p className="flex h-6 flex-1 items-end text-sm">
                  Install required dependencies
                </p>
                {lang === Language.TYPESCRIPT && (
                  <ModuleInstaller
                    languages={["typescript"]}
                    packageNames="braintrust"
                  />
                )}
                {lang === Language.PYTHON && (
                  <ModuleInstaller
                    languages={["python"]}
                    packageNames="braintrust"
                  />
                )}
              </div>
              <div className="relative">
                <div className="mb-2 flex items-center justify-between text-sm">
                  Configure environment variables
                  {showKeyGeneration && (
                    <Button
                      size="xs"
                      className={cn("bg-background", {
                        invisible: generatedApiKey,
                      })}
                      onClick={() => createKey(org.id)}
                      disabled={creating}
                    >
                      {creating ? (
                        <div className="flex items-center">
                          Generating API key <Spinner className="ml-1" />
                        </div>
                      ) : (
                        "Generate API key"
                      )}
                    </Button>
                  )}
                </div>
                <CodeToCopy
                  inline
                  inlineClassName="from-primary-50"
                  data={
                    selectedProviderConfig?.environmentVariables
                      ? Object.entries(
                          selectedProviderConfig.environmentVariables,
                        )
                          .map(([key, value]) => `${key}=${value}`)
                          .join("\n")
                      : ""
                  }
                  language="bash"
                />
              </div>
              <div className="flex flex-col">
                <div className="mb-2 flex items-end gap-3">
                  <div className="flex h-6 w-full flex-row items-center justify-between">
                    <div className="text-sm">Trace your LLM calls</div>
                    {selectedProviderConfig?.docsUrl && (
                      <Link
                        href={selectedProviderConfig.docsUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={cn(
                          buttonVariants({
                            variant: "ghost",
                            size: "xs",
                          }),
                          "text-primary-400",
                        )}
                      >
                        Learn more <ArrowUpRight className="size-3" />
                      </Link>
                    )}
                  </div>
                  <div className="h-6 empty:hidden">
                    {llmProvider === SdkIntegration.VERCEL && (
                      <Tabs value={framework}>
                        <TabsList className="h-6">
                          {Object.values(VercelFramework).map((framework) => (
                            <TabsTrigger
                              key={framework}
                              value={framework}
                              className="h-5 text-xs"
                              onClick={() => setFramework(framework)}
                            >
                              {framework}.js
                            </TabsTrigger>
                          ))}
                        </TabsList>
                      </Tabs>
                    )}
                  </div>
                </div>
                <div className="flex flex-col gap-2">
                  {providerCodeExamples.map(
                    (example: string, index: number) => (
                      <CodeToCopy
                        key={index}
                        language={
                          lang === Language.PYTHON ? "python" : "javascript"
                        }
                        data={example}
                      />
                    ),
                  )}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
