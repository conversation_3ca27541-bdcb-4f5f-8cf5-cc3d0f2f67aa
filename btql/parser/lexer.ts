import { z } from "zod";
import { posSchema } from "./ast";

export const reservedWords = [
  "true",
  "false",
  "null",
  "and",
  "or",
  "not",
  "is",
  "as",
  "includes",
  "contains",
  "like",
  "ilike",
  "match",
  "in",
  "filter",
  "pivot",
  "unpivot",
  "dimensions",
  "measures",
  "select",
  "infer",
  "from",
  "sort",
  "asc",
  "desc",
  "limit",
  "cursor",
  "interval",
  "comparison_key",
  "weighted_scores",
  "custom_columns",
  "preview_length",
  "inference_budget",
] as const;

export const keywords = [
  "year",
  "month",
  "day",
  "hour",
  "minute",
  "second",
  "millisecond",
  "microsecond",
  "spans",
  "traces",
] as const;

const keywordSchema = z.enum([...reservedWords, ...keywords]);

const rules = {
  // Punctuation
  doubleSlash: "//",
  doubleDash: "--",
  slashStar: "/*",
  starSlash: "*/",
  lcurly: "{",
  rcurly: "}",
  lsquare: "[",
  rsquare: "]",
  lparen: "(",
  rparen: ")",
  comma: ",",
  colon: ":",
  period: ".",
  equalOp: "=",
  notEqualOp: "!=",
  doubleAngle: "<>",
  lessThanOrEqual: "<=",
  lessThan: "<",
  greaterThanOrEqual: ">=",
  greaterThan: ">",
  plus: "+",
  minus: "-",
  star: "*",
  slash: "/",
  percent: "%",
  pipe: "|",
  question: "?",

  // Literals
  word: /[a-zA-Z_][a-zA-Z0-9_]*/,
  singleQuotedString: /'(?:[^\\']|\\(?:[bfnrtv'\\/]|u[0-9a-fA-F]{4}))*?'/,
  doubleQuotedString: /"(?:[^\\"]|\\(?:[bfnrtv"\\/]|u[0-9a-fA-F]{4}))*?"/,
  backtickQuotedString: /`(?:[^\\`]|\\(?:[bfnrtv`\\/]|u[0-9a-fA-F]{4}))*?`/,
  percentNumber: /-?(0|[1-9]\d*)(\.\d+)?([eE][+-]?\d+)?%/,
  number: /-?(0|[1-9]\d*)(\.\d+)?([eE][+-]?\d+)?/,

  // Whitespace
  whiteSpace: /[ \t\n\r]+/,
};

export type Token = keyof typeof rules | (typeof reservedWords)[number];

export type TokenInfo = {
  type: Token;
  value: string;
} & z.infer<typeof posSchema>;

export class TokenizerError extends Error {
  public line: number;
  public col: number;
  constructor({ line, col, msg }: { line: number; col: number; msg: string }) {
    super(`${msg} at line ${line}, col ${col}`);
    this.line = line;
    this.col = col;
  }
}

export class Tokenizer {
  private skipComments: boolean = false;
  private cursor: number = 0;
  private _line = 1;
  private _col = 1;

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  private rules = Object.fromEntries(
    Object.entries(rules).map(
      ([token, pattern]) =>
        [
          token,
          new RegExp(
            typeof pattern === "string"
              ? pattern.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")
              : pattern,
            "y" + (typeof pattern === "object" ? pattern.flags : ""),
          ),
        ] as const,
    ),
  ) as Record<keyof typeof rules, RegExp>;
  private current: TokenInfo | null = null;

  constructor(
    private text: string,
    opts?: { skipComments?: boolean },
  ) {
    this.skipComments = opts?.skipComments ?? false;
  }

  public get eof() {
    return this.cursor >= this.text.length;
  }

  public snapshot(): { cursor: number; line: number; col: number } {
    return { cursor: this.cursor, line: this._line, col: this._col };
  }

  public restore(snapshot: { cursor: number; line: number; col: number }) {
    this.cursor = snapshot.cursor;
    this._line = snapshot.line;
    this._col = snapshot.col;
  }

  public peekToken(): TokenInfo | null {
    if (this.eof) {
      return null;
    }

    for (const [token, pattern] of Object.entries(this.rules)) {
      pattern.lastIndex = this.cursor;
      const match = pattern.exec(this.text);
      if (match && match.index === this.cursor) {
        if (token === "whiteSpace") {
          this.cursor += match[0].length;
          this.updateLineCol(match[0]);
          return this.peekToken();
        } else if (token === "slashStar") {
          const startLine = this._line;
          const startCol = this._col;
          // match.index is the current this.cursor, and match[0] is "/*"
          const endCommentMarkerOffset = this.text.indexOf(
            "*/",
            match.index + match[0].length,
          );
          if (endCommentMarkerOffset === -1) {
            throw new TokenizerError({
              line: startLine,
              col: startCol,
              msg: `Unterminated block comment`,
            });
          }

          const commentEndOffset = endCommentMarkerOffset + 2; // +2 for "*/" length

          if (this.skipComments) {
            this.cursor = commentEndOffset;
            this.updateLineCol(this.text.slice(match.index, this.cursor));
            return this.peekToken();
          } else {
            const commentValue = this.text.slice(match.index, commentEndOffset);
            return {
              type: "slashStar",
              value: commentValue,
              line: startLine,
              col: startCol,
            };
          }
        } else if (token === "doubleSlash" || token === "doubleDash") {
          const startLine = this._line;
          const startCol = this._col;
          const end = this.text.indexOf("\n", this.cursor + 2);

          let commentEndOffset;
          if (end === -1) {
            commentEndOffset = this.text.length;
          } else {
            commentEndOffset = end + 1;
          }

          if (this.skipComments) {
            this.cursor = commentEndOffset;
            this.updateLineCol(this.text.slice(match.index, this.cursor));
            return this.peekToken();
          } else {
            const commentValue = this.text.slice(match.index, commentEndOffset);
            return {
              type: token,
              value: commentValue,
              line: startLine,
              col: startCol,
            };
          }
        }

        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        let matchedToken = token as Token;
        const value = match[0];
        if (
          token === "word" &&
          keywordSchema.safeParse(value.toLowerCase()).success
        ) {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          matchedToken = value.toLowerCase() as Token;
        }

        const line = this._line;
        const col = this._col;

        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        return { type: matchedToken as Token, value, line, col };
      }
    }

    throw new TokenizerError({
      line: this._line,
      col: this._col,
      msg: `Unexpected character`,
    });
  }

  private updateLineCol(value: string) {
    const numLines = value.split("\n").length - 1;
    this._line += numLines;
    this._col =
      numLines === 0
        ? this._col + value.length
        : value.length - value.lastIndexOf("\n");
  }

  public nextToken(): TokenInfo | null {
    const token = this.peekToken();
    if (token) {
      this.cursor += token.value.length;
      this.current = token;
      this.updateLineCol(token.value);
    }
    return token;
  }

  public currentToken(): TokenInfo | null {
    return this.current;
  }

  public get line(): number {
    return this._line;
  }

  public get col() {
    return this._col;
  }

  public substringCursor(before: number, after: number): string {
    return this.text.substring(
      Math.max(this.cursor - before, 0),
      Math.min(this.cursor + after, this.text.length),
    );
  }

  public *tokens(): Generator<TokenInfo> {
    let token: TokenInfo | null;
    while ((token = this.nextToken())) {
      yield token;
    }
  }
}

export function tokenize(
  input: string,
  opts?: { skipComments?: boolean },
): Tokenizer {
  return new Tokenizer(input, opts);
}
