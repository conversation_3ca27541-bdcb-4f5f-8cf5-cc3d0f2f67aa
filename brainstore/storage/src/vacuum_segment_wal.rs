use std::{path::PathBuf, sync::Arc};

use async_stream::try_stream;
use clap::Parser;
use futures::stream::BoxStream;
use futures::{StreamExt, TryStreamExt};
use lazy_static::lazy_static;
use otel_common::opentelemetry::metrics::Counter;
use serde::{Deserialize, Serialize};
use util::{
    anyhow::{anyhow, Result},
    chrono::{DateTime, Duration, Utc},
    system_types::FullObjectId,
    uuid::Uuid,
};

use crate::{
    config_with_store::StoreInfo,
    deletion_log::{DeletionLogArgs, DeletionLogStreamExt},
    global_locks_manager::GlobalLocksManager,
    global_store::{GlobalStore, TaskInfo, VacuumSegmentWalInfo},
    object_and_global_store_wal::ObjectAndGlobalStoreWal,
    timer::{OtelCounterGuard, OtelTimer},
    vacuum::{CommonVacuumOptions, VacuumType},
    wal::WALScope,
};

type Error = util::anyhow::Error;

pub struct VacuumSegmentWalMeters {
    pub run: OtelTimer,
    pub run_errors: Counter<u64>,
    pub iteration: OtelTimer,

    pub segments_considered: Counter<u64>,
    pub segments_processed: Counter<u64>,

    pub batch: OtelTimer,
    pub delete_stream_errors: Counter<u64>,

    pub wal_entries_purged: Counter<u64>,
    pub files_deleted: Counter<u64>,
    pub bytes_deleted: Counter<u64>,
}

impl Default for VacuumSegmentWalMeters {
    fn default() -> Self {
        let meter = otel_common::opentelemetry::global::meter("brainstore");
        Self {
            run: OtelTimer::new(&meter, "brainstore.storage.vacuum_segment_wal.run"),
            run_errors: meter
                .u64_counter("brainstore.storage.vacuum_segment_wal.run.errors")
                .build(),
            iteration: OtelTimer::new(&meter, "brainstore.storage.vacuum_segment_wal.iteration"),

            segments_considered: meter
                .u64_counter("brainstore.storage.vacuum_segment_wal.segments_considered")
                .build(),
            segments_processed: meter
                .u64_counter("brainstore.storage.vacuum_segment_wal.segments_processed")
                .build(),

            batch: OtelTimer::new(&meter, "brainstore.storage.vacuum_segment_wal.batch"),
            delete_stream_errors: meter
                .u64_counter("brainstore.storage.vacuum_segment_wal.delete_stream.errors")
                .build(),
            wal_entries_purged: meter
                .u64_counter("brainstore.storage.vacuum_segment_wal.wal_entries_purged")
                .build(),
            files_deleted: meter
                .u64_counter("brainstore.storage.vacuum_segment_wal.files_deleted")
                .build(),
            bytes_deleted: meter
                .u64_counter("brainstore.storage.vacuum_segment_wal.bytes_deleted")
                .build(),
        }
    }
}

lazy_static! {
    pub static ref VACUUM_SEGMENT_WAL_METERS: VacuumSegmentWalMeters =
        VacuumSegmentWalMeters::default();
}

#[derive(Clone, Debug)]
pub struct VacuumSegmentWalInput<'a> {
    // If object_ids is None, all objects will be vacuumed.
    pub object_ids: Option<&'a [FullObjectId<'a>]>,
    pub global_store: Arc<dyn GlobalStore>,
    pub index_store: StoreInfo,
    pub locks_manager: &'a dyn GlobalLocksManager,
    pub dry_run: bool,
}

#[derive(Clone, Debug, Default)]
pub struct VacuumSegmentWalOptionalInput {
    pub testing_force_query_segment_wal_entries_error_for_segment_id: Option<Uuid>,
    pub testing_force_list_error_for_segment_id: Option<Uuid>,
}

#[derive(Clone, Debug, Parser, Serialize, Deserialize)]
pub struct VacuumSegmentWalOptions {
    #[arg(
        long,
        default_value_t = default_vacuum_segment_wal_segment_batch_size(),
        help = "Segment batch size for vacuuming segment WAL files.",
        env = "BRAINSTORE_VACUUM_SEGMENT_WAL_SEGMENT_BATCH_SIZE",
    )]
    #[serde(default = "default_vacuum_segment_wal_segment_batch_size")]
    pub vacuum_segment_wal_segment_batch_size: usize,
    #[arg(
        long,
        default_value_t = default_vacuum_segment_wal_query_purged_wal_filenames_batch_size(),
        help = "Batch size for vacuuming segment WAL files.",
        env = "BRAINSTORE_VACUUM_SEGMENT_WAL_QUERY_PURGED_WAL_FILENAMES_BATCH_SIZE",
    )]
    #[serde(default = "default_vacuum_segment_wal_query_purged_wal_filenames_batch_size")]
    pub vacuum_segment_wal_query_purged_wal_filenames_batch_size: usize,
    #[arg(
        long,
        default_value_t = default_vacuum_segment_wal_entry_expiration_seconds(),
        help = "How long to retain soft-deleted segment WAL entries before purging them.",
        env = "BRAINSTORE_VACUUM_SEGMENT_WAL_ENTRY_EXPIRATION_SECONDS",
    )]
    #[serde(default = "default_vacuum_segment_wal_entry_expiration_seconds")]
    pub vacuum_segment_wal_entry_expiration_seconds: i64,
    #[arg(
        long,
        default_value_t = default_vacuum_segment_wal_period_seconds(),
        help = "Minimum wait between vacuum operations on a segment.",
        env = "BRAINSTORE_VACUUM_SEGMENT_WAL_PERIOD_SECONDS",
    )]
    #[serde(default = "default_vacuum_segment_wal_period_seconds")]
    pub vacuum_segment_wal_period_seconds: i64,
    // If false, only vacuum files in the segment WAL directory that match the expected format for
    // segment WAL filenames (a single part consisting of a valid UUID).
    #[arg(
        long,
        default_value_t = false,
        help = "Delete unrecognized files in the segment WAL directory.",
        env = "BRAINSTORE_VACUUM_SEGMENT_WAL_DELETE_UNRECOGNIZED_FILES"
    )]
    #[serde(default)]
    pub vacuum_segment_wal_delete_unrecognized_files: bool,
}

impl Default for VacuumSegmentWalOptions {
    fn default() -> Self {
        Self {
            vacuum_segment_wal_segment_batch_size: default_vacuum_segment_wal_segment_batch_size(),
            vacuum_segment_wal_query_purged_wal_filenames_batch_size:
                default_vacuum_segment_wal_query_purged_wal_filenames_batch_size(),
            vacuum_segment_wal_entry_expiration_seconds:
                default_vacuum_segment_wal_entry_expiration_seconds(),
            vacuum_segment_wal_period_seconds: default_vacuum_segment_wal_period_seconds(),
            vacuum_segment_wal_delete_unrecognized_files: false,
        }
    }
}

fn default_vacuum_segment_wal_segment_batch_size() -> usize {
    100
}

fn default_vacuum_segment_wal_query_purged_wal_filenames_batch_size() -> usize {
    1000
}

/// WAL entries soft-deleted within this number of seconds ago will not be purged.
fn default_vacuum_segment_wal_entry_expiration_seconds() -> i64 {
    7 * 24 * 60 * 60 // 1 week
}

fn default_vacuum_segment_wal_period_seconds() -> i64 {
    15 * 60 // 15 minutes
}

#[derive(Clone, Debug, Default, Parser, Serialize, Deserialize)]
pub struct VacuumSegmentWalFullOptions {
    #[command(flatten)]
    #[serde(flatten)]
    pub common_vacuum_opts: CommonVacuumOptions,
    #[command(flatten)]
    #[serde(flatten)]
    pub vacuum_segment_wal_opts: VacuumSegmentWalOptions,
}

#[derive(Serialize)]
pub struct VacuumSegmentWalStatelessOutput {
    pub success: bool,
    pub num_processed_segments: usize,
    pub planned_num_purged_entries: u64,
    pub num_purged_entries: u64,
    pub planned_num_deleted_files: usize,
    pub num_deleted_files: usize,
    pub error: Option<String>,
}

#[tracing::instrument(skip(input, optional_input))]
pub async fn vacuum_segment_wal_stateless<'a>(
    input: VacuumSegmentWalInput<'a>,
    optional_input: VacuumSegmentWalOptionalInput,
    options: VacuumSegmentWalFullOptions,
) -> VacuumSegmentWalStatelessOutput {
    let input = &input;
    let optional_input = &optional_input;

    let object_ids = input.object_ids.unwrap_or(&[]);
    if object_ids.is_empty() {
        return VacuumSegmentWalStatelessOutput {
            success: false,
            num_processed_segments: 0,
            planned_num_purged_entries: 0,
            num_purged_entries: 0,
            planned_num_deleted_files: 0,
            num_deleted_files: 0,
            error: Some(
                "Must provide at least one object ID for stateless segment WAL vacuum".to_string(),
            ),
        };
    }

    let start_ts = Utc::now();

    let mut num_processed_segments = 0;
    let mut planned_num_purged_entries = 0;
    let mut num_purged_entries = 0;
    let mut planned_num_deleted_files = 0;
    let mut num_deleted_files = 0;

    // Get all segments for the provided object_ids. We don't filter by eligibility for the
    // stateless command.
    let segment_ids = {
        match input.global_store.list_segment_ids(object_ids, None).await {
            Err(e) => {
                tracing::error!("Error listing segment IDs: {}", e);
                return VacuumSegmentWalStatelessOutput {
                    success: false,
                    num_processed_segments: 0,
                    planned_num_purged_entries: 0,
                    num_purged_entries: 0,
                    planned_num_deleted_files: 0,
                    num_deleted_files: 0,
                    error: Some(e.to_string()),
                };
            }
            Ok(segment_ids) => segment_ids.into_iter().flatten().collect::<Vec<_>>(),
        }
    };
    if segment_ids.is_empty() {
        tracing::debug!(
            "No segments found for the specified object IDs, exiting stateless vacuum-segment-wal"
        );
        return VacuumSegmentWalStatelessOutput {
            success: true,
            num_processed_segments: 0,
            planned_num_purged_entries: 0,
            num_purged_entries: 0,
            planned_num_deleted_files: 0,
            num_deleted_files: 0,
            error: None,
        };
    }

    let max_file_last_modified = start_ts
        - Duration::seconds(
            options
                .common_vacuum_opts
                .vacuum_deletion_grace_period_seconds,
        );

    for batch in segment_ids.chunks(
        options
            .vacuum_segment_wal_opts
            .vacuum_segment_wal_segment_batch_size,
    ) {
        let batch_segment_ids = batch.to_vec();
        let batch_size = batch_segment_ids.len();
        tracing::debug!("Processing batch of {} segments", batch_size);

        match vacuum_segment_wal_batch(VacuumSegmentWalBatchArgs {
            global_store: input.global_store.clone(),
            index_store: &input.index_store,
            segment_ids: batch_segment_ids.clone(),
            entry_expiration_seconds: options
                .vacuum_segment_wal_opts
                .vacuum_segment_wal_entry_expiration_seconds,
            max_file_last_modified,
            query_purged_wal_filenames_batch_size: options
                .vacuum_segment_wal_opts
                .vacuum_segment_wal_query_purged_wal_filenames_batch_size,
            delete_unrecognized_files: options
                .vacuum_segment_wal_opts
                .vacuum_segment_wal_delete_unrecognized_files,
            deletion_log_batch_size: options.common_vacuum_opts.vacuum_deletion_log_batch_size,
            dry_run: input.dry_run,
            testing_force_list_error_for_segment_id: optional_input
                .testing_force_list_error_for_segment_id,
        })
        .await
        {
            Ok(batch_output) => {
                num_processed_segments += batch_size;
                planned_num_purged_entries += batch_output.planned_num_purged_entries;
                num_purged_entries += batch_output.num_purged_entries;
                planned_num_deleted_files += batch_output.planned_num_deleted_files;
                num_deleted_files += batch_output.num_deleted_files;

                if input.dry_run {
                    tracing::info!(
                        batch_size = batch_size,
                        planned_num_deleted_files = batch_output.planned_num_deleted_files,
                        "Dry run: processed stateless vacuum-segment-wal batch",
                    );
                } else {
                    tracing::info!(
                        batch_size = batch_size,
                        num_deleted_files = batch_output.num_deleted_files,
                        "Processed stateless vacuum-segment-wal batch",
                    );
                }
            }
            Err(e) => {
                tracing::error!("Error processing stateless vacuum-segment-wal batch: {}", e);
                return VacuumSegmentWalStatelessOutput {
                    success: false,
                    num_processed_segments,
                    planned_num_purged_entries,
                    num_purged_entries,
                    planned_num_deleted_files,
                    num_deleted_files,
                    error: Some(e.to_string()),
                };
            }
        }
    }

    tracing::info!(
        dry_run = input.dry_run,
        num_processed_segments = num_processed_segments,
        planned_num_purged_entries = planned_num_purged_entries,
        num_purged_entries = num_purged_entries,
        planned_num_deleted_files = planned_num_deleted_files,
        num_deleted_files = num_deleted_files,
        "Completed stateless vacuum-segment-wal",
    );

    VacuumSegmentWalStatelessOutput {
        success: true,
        num_processed_segments,
        planned_num_purged_entries,
        num_purged_entries,
        planned_num_deleted_files,
        num_deleted_files,
        error: None,
    }
}

#[derive(Default, Serialize)]
pub struct VacuumSegmentWalOutput {
    pub num_processed_segments: usize,
    pub planned_num_purged_entries: u64,
    pub num_purged_entries: u64,
    pub planned_num_deleted_files: usize,
    pub num_deleted_files: usize,
}

/// This function completes two tasks:
///
///   1. Purging soft-deleted WAL entries from the global store.
///       - Only files with a `deleted_at` older than the deletion grace period are purged.
///   2. Deleting WAL files that are not referenced by any WAL entries in the global store.
///       - Only files with a `last_modified` older than the deletion grace period are deleted.
///       - If a WAL file is referenced only by soft-deleted WAL entries, we still don't delete
///         it, since we need to retain WAL files for soft-deleted entries in order to be able
///         to restore the files if we revert the deletes.
///
/// Procedure:
///
/// Fetch live segments in batches, starting with the segments whose last successful vacuum is
/// most behind the last write to the segment. Segments that have never been vacuumed are
/// prioritized first.
/// We skip a segment if either of the following is true:
///
///   1. We haven't waited long enough (vacuum_period_seconds) since this segment's last vacuum operation.
///   2. The last successful vacuum occurred enough time after the last write to the segment that
///      we can assume no soft-deleted WAL entries still need to be purged AND no additional WAL
///      files were written to the segment in the meantime. This is guaranteed if:
///
///      last_successful_start_ts - last_written_ts >
///        max(wal_entry_expiration_seconds, deletion_grace_period_seconds) + last_written_slop_seconds
///
/// Eligible segments are processed in batches. If a segment errors, we log the error to the global
/// store. Otherwise, we update `last_successful_start_ts` for every segment in the batch.
#[tracing::instrument(err, skip(input, optional_input), fields(object_ids = ?input.object_ids, dry_run = input.dry_run))]
pub async fn vacuum_segment_wal<'a>(
    input: VacuumSegmentWalInput<'a>,
    optional_input: VacuumSegmentWalOptionalInput,
    options: &VacuumSegmentWalFullOptions,
) -> Result<VacuumSegmentWalOutput> {
    let vacuum_type = VacuumType::VacuumSegmentWal;
    let _lock = match input
        .locks_manager
        .try_write(&vacuum_type.lock_name())
        .await?
    {
        Some(lock) => lock,
        None => {
            tracing::debug!(
                "Skipping vacuum-segment-wal because another worker is already running it"
            );
            return Ok(VacuumSegmentWalOutput::default());
        }
    };

    let start_ts = Utc::now();
    let _run_timer = OtelCounterGuard::new(&VACUUM_SEGMENT_WAL_METERS.run);
    tracing::info!(
        start_ts = ?start_ts,
        "Starting vacuum-segment-wal loop"
    );

    let input = &input;
    let optional_input = &optional_input;
    let max_last_successful_start_minus_last_written_seconds =
        std::cmp::max(
            options
                .vacuum_segment_wal_opts
                .vacuum_segment_wal_entry_expiration_seconds,
            options
                .common_vacuum_opts
                .vacuum_deletion_grace_period_seconds,
        ) + options.common_vacuum_opts.vacuum_last_written_slop_seconds;

    let mut num_processed_segments = 0;
    let mut num_purged_entries = 0;
    let mut planned_num_purges = 0;
    let mut planned_num_deletes = 0;
    let mut num_deleted_files = 0;
    let mut i = 0;
    loop {
        let _iteration_timer = OtelCounterGuard::new(&VACUUM_SEGMENT_WAL_METERS.iteration);
        let segment_ids = input
            .global_store
            .query_vacuum_segment_ids(
                vacuum_type,
                options
                    .vacuum_segment_wal_opts
                    .vacuum_segment_wal_segment_batch_size,
                max_last_successful_start_minus_last_written_seconds,
                options
                    .vacuum_segment_wal_opts
                    .vacuum_segment_wal_period_seconds,
                start_ts,
                input.object_ids,
            )
            .await?;

        if segment_ids.is_empty() {
            tracing::debug!(
                "No remaining segments are eligible for vacuum-segment-wal, exiting loop",
            );
            break;
        }

        let num_segment_ids = segment_ids.len();
        tracing::info!(
            iteration = i,
            num_segment_ids = num_segment_ids,
            "Fetched segment_ids for vacuum-segment-wal",
        );

        let max_file_last_modified = start_ts
            - Duration::seconds(
                options
                    .common_vacuum_opts
                    .vacuum_deletion_grace_period_seconds,
            );

        let batch_output = match vacuum_segment_wal_batch(VacuumSegmentWalBatchArgs {
            global_store: input.global_store.clone(),
            index_store: &input.index_store,
            segment_ids: segment_ids.clone(),
            entry_expiration_seconds: options
                .vacuum_segment_wal_opts
                .vacuum_segment_wal_entry_expiration_seconds,
            max_file_last_modified,
            query_purged_wal_filenames_batch_size: options
                .vacuum_segment_wal_opts
                .vacuum_segment_wal_query_purged_wal_filenames_batch_size,
            delete_unrecognized_files: options
                .vacuum_segment_wal_opts
                .vacuum_segment_wal_delete_unrecognized_files,
            deletion_log_batch_size: options.common_vacuum_opts.vacuum_deletion_log_batch_size,
            dry_run: input.dry_run,
            testing_force_list_error_for_segment_id: optional_input
                .testing_force_list_error_for_segment_id,
        })
        .await
        {
            Ok(output) => output,
            Err(e) => {
                input
                    .global_store
                    .upsert_segment_task_info(
                        &segment_ids,
                        &TaskInfo::VacuumSegmentWal(VacuumSegmentWalInfo {
                            start_ts: Some(start_ts),
                            error: Some(serde_json::Value::String(e.to_string())),
                            ..Default::default()
                        }),
                    )
                    .await?;
                return Err(e);
            }
        };

        tracing::debug!(
            iteration = i,
            num_segment_ids = num_segment_ids,
            num_purged_entries = batch_output.num_purged_entries,
            num_deleted_files = batch_output.num_deleted_files,
            "Completed vacuum-segment-wal batch",
        );

        // The batch succeeded, so bump `last_successful_start_ts` for the included segments.
        // and update the task info.
        input
            .global_store
            .upsert_segment_vacuum_last_successful_start_ts(
                &segment_ids,
                VacuumType::VacuumSegmentWal,
                start_ts,
            )
            .await?;
        input
            .global_store
            .upsert_segment_task_info(
                &segment_ids,
                &TaskInfo::VacuumSegmentWal(VacuumSegmentWalInfo {
                    start_ts: Some(start_ts),
                    completed_ts: Some(Utc::now()),
                    ..Default::default()
                }),
            )
            .await?;

        num_processed_segments += num_segment_ids;
        num_purged_entries += batch_output.num_purged_entries;
        planned_num_purges += batch_output.planned_num_purged_entries;
        planned_num_deletes += batch_output.planned_num_deleted_files;
        num_deleted_files += batch_output.num_deleted_files;
        i += 1;
    }

    tracing::info!(
        num_processed_segments = num_processed_segments,
        num_purged_entries = num_purged_entries,
        num_deleted_files = num_deleted_files,
        "Completed vacuum-segment-wal loop",
    );

    Ok(VacuumSegmentWalOutput {
        num_processed_segments,
        num_purged_entries,
        planned_num_purged_entries: planned_num_purges,
        planned_num_deleted_files: planned_num_deletes,
        num_deleted_files,
    })
}

#[derive(Clone, Debug)]
struct VacuumSegmentWalBatchArgs<'a> {
    global_store: Arc<dyn GlobalStore>,
    index_store: &'a StoreInfo,
    segment_ids: Vec<Uuid>,
    entry_expiration_seconds: i64,
    max_file_last_modified: DateTime<Utc>,
    query_purged_wal_filenames_batch_size: usize,
    delete_unrecognized_files: bool,
    deletion_log_batch_size: usize,
    dry_run: bool,
    testing_force_list_error_for_segment_id: Option<Uuid>,
}

struct VacuumSegmentWalBatchOutput {
    planned_num_purged_entries: u64,
    num_purged_entries: u64,
    planned_num_deleted_files: usize,
    num_deleted_files: usize,
}

#[tracing::instrument(
    err,
    skip(args),
    fields(
        dry_run = args.dry_run,
        num_segment_ids = args.segment_ids.len(),
        entry_expiration_seconds = args.entry_expiration_seconds,
        max_file_last_modified = ?args.max_file_last_modified,
    ),
)]
async fn vacuum_segment_wal_batch(
    args: VacuumSegmentWalBatchArgs<'_>,
) -> Result<VacuumSegmentWalBatchOutput, Error> {
    let res = vacuum_segment_wal_batch_inner(args).await;
    if let Err(ref e) = res {
        tracing::error!(
            error = ?e,
            "Error running vacuum-segment-wal batch",
        );
        VACUUM_SEGMENT_WAL_METERS.run_errors.add(1, &[]);
    }
    res
}

async fn vacuum_segment_wal_batch_inner(
    args: VacuumSegmentWalBatchArgs<'_>,
) -> Result<VacuumSegmentWalBatchOutput, Error> {
    let _batch_timer = OtelCounterGuard::new(&VACUUM_SEGMENT_WAL_METERS.batch);
    let num_segments = args.segment_ids.len();
    VACUUM_SEGMENT_WAL_METERS
        .segments_considered
        .add(num_segments as u64, &[]);

    let segment_index_wal = ObjectAndGlobalStoreWal {
        object_store: args.index_store.store.clone(),
        global_store: args.global_store.clone(),
        directory: args.index_store.directory.clone(),
        store_prefix: args.index_store.prefix.clone(),
        store_type: args.index_store.store_type,
    };

    // First, purge soft-deleted WAL entries older than `entry_expiration_seconds` from the
    // global store. For dry runs, just count the entries without actually purging them.
    let (num_purged_entries, planned_num_purged_entries) = if args.dry_run {
        let count = args
            .global_store
            .count_deleted_segment_wal_entries(&args.segment_ids, args.entry_expiration_seconds)
            .await?;
        (0, count)
    } else {
        let purged = args
            .global_store
            .purge_deleted_segment_wal_entries(&args.segment_ids, args.entry_expiration_seconds)
            .await?;
        (purged, purged)
    };

    if args.dry_run {
        tracing::info!(
            num_segments,
            planned_num_purged_entries,
            "Dry run for vacuum-segment-wal batch: would purge soft-deleted WAL entries, will now count unused WAL files",
        );
    }

    VACUUM_SEGMENT_WAL_METERS
        .wal_entries_purged
        .add(num_purged_entries, &[]);

    // Next, delete segment WAL files that no longer have corresponding entries in the global
    // store. WAL files corresponding to soft-deleted WAL entries are not deleted, because we
    // need to keep them around in case we want to restore the entries later.
    let mut total_bytes = 0u64;

    let paths_to_delete_stream: BoxStream<Result<PathBuf, object_store::Error>> = {
        let total_bytes = &mut total_bytes;

        try_stream! {
        for segment_id in args.segment_ids {
            let wal_directory = segment_index_wal.wal_directory(WALScope::Segment(segment_id));
            let wal_directory_str = wal_directory
                .to_str()
                .ok_or(anyhow!("Invalid WAL directory"))?;
            let wal_prefix = object_store::path::Path::from(wal_directory_str);

            let mut listing = segment_index_wal
                .object_store
                .list(Some(&wal_prefix))
                // Don't delete the file if it's within the deletion grace period. This allows us to
                // avoid taking a write lock on the segment.
                .try_filter(|object_meta| futures::future::ready(object_meta.last_modified < args.max_file_last_modified))
                .try_chunks(args.query_purged_wal_filenames_batch_size);

            while let Some(batch) = listing.try_next().await? {
                let mut valid_filenames: Vec<Uuid> = Vec::new();
                // We only track `valid_paths` to compare to the joined paths as
                // as a sanity check. We could remove this later.
                let mut valid_paths: Vec<PathBuf> = Vec::new();
                let mut valid_file_sizes: std::collections::HashMap<Uuid, u64> = std::collections::HashMap::new();

                for object_meta in batch {
                    let path = object_meta.location;
                    let path_str = path.to_string();

                    let path_parts = path.prefix_match(&wal_prefix).ok_or(
                        anyhow!("Path does not have expected prefix: {}", path_str)
                    )?.collect::<Vec<_>>();

                    if path_parts.is_empty() {
                        continue;
                    }

                    if path_parts.len() == 1 {
                        let parse_result = Uuid::parse_str(path_parts[0].as_ref());
                        if let Ok(uuid) = parse_result {
                            valid_filenames.push(uuid);
                            valid_paths.push(PathBuf::from(path_str));
                            valid_file_sizes.insert(uuid, object_meta.size);
                            continue;
                        }
                    }

                    // If the path is not a single part consisting of a valid UUID, it's not a valid
                    // segment WAL file. Only delete it if `delete_unrecognized_files` is true.
                    tracing::warn!(
                        path = %path_str,
                        "Found unrecognized file in segment WAL directory",
                    );
                    if !args.delete_unrecognized_files {
                        continue;
                    }

                    // Check that the joined path matches the original path.
                    let joined_path: object_store::path::Path = wal_prefix
                        .parts()
                        .chain(path_parts.into_iter())
                        .collect();
                    if joined_path != path {
                        Err(anyhow!(
                            "Joined path '{}' doesn't match original path '{}'",
                            joined_path.to_string(),
                            path.to_string()
                        ))?;
                    }

                    *total_bytes += object_meta.size;
                    yield PathBuf::from(path_str);
                }

                if !valid_filenames.is_empty() {
                    // Actually, the query batch will be smaller than the input batch size if there
                    // are junk files in the segment WAL directory. But this should be rare.
                    let purged_filenames = segment_index_wal.global_store
                        .query_purged_wal_filenames(segment_id, &valid_filenames, None)
                        .await
                        .map_err(|e| anyhow!("Error querying purged WAL filenames: {}", e))?;
                    for uuid in purged_filenames {
                        let path = wal_directory.join(uuid.to_string());
                        assert!(valid_paths.contains(&path));
                        if let Some(size) = valid_file_sizes.get(&uuid) {
                            *total_bytes += size;
                        }
                        yield path;
                    }
                }
            }

            if args.testing_force_list_error_for_segment_id == Some(segment_id) {
                Err(anyhow!("Forced listing error for testing purposes"))?;
            }
        }
        }
    }
    .map_err(|e: util::anyhow::Error| object_store::Error::Generic {
        store: "vacuum_segment_wal",
        source: Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))
    })
    .boxed();

    let paths_to_delete_stream = paths_to_delete_stream.with_deletion_log(DeletionLogArgs {
        store: args.index_store.store.clone(),
        deletion_log_prefix: &args.index_store.prefix,
        dry_run: args.dry_run,
        batch_size: args.deletion_log_batch_size,
    });

    if args.dry_run {
        let planned_num_deleted_files = paths_to_delete_stream
            .try_fold(0, |count, _| async move { Ok(count + 1) })
            .await?;
        tracing::info!(
            num_segments = num_segments,
            planned_num_deleted_files = planned_num_deleted_files,
            "Dry run for vacuum-segment-wal batch complete",
        );
        return Ok(VacuumSegmentWalBatchOutput {
            planned_num_purged_entries,
            num_purged_entries,
            planned_num_deleted_files,
            num_deleted_files: 0,
        });
    }

    let delete_stream = args
        .index_store
        .directory
        .delete_stream(paths_to_delete_stream);

    let (num_deleted_files, num_errors) = delete_stream
        .fold((0, 0), |(deleted_count, error_count), res| async move {
            match res {
                Ok(_) => (deleted_count + 1, error_count),
                Err(e) => {
                    tracing::warn!(error = ?e, "vacuum-segment-wal delete stream error");
                    (deleted_count, error_count + 1)
                }
            }
        })
        .await;

    VACUUM_SEGMENT_WAL_METERS
        .files_deleted
        .add(num_deleted_files as u64, &[]);

    if num_errors > 0 {
        VACUUM_SEGMENT_WAL_METERS
            .delete_stream_errors
            .add(num_errors as u64, &[]);
        tracing::warn!(
            deleted_files = num_deleted_files,
            errors = num_errors,
            planned_deletes = num_deleted_files + num_errors,
            "Vacuum-segment-wal batch completed with errors"
        );
        return Err(anyhow!("vacuum-segment-wal batch completed with errors"));
    }

    VACUUM_SEGMENT_WAL_METERS
        .segments_processed
        .add(num_segments as u64, &[]);
    VACUUM_SEGMENT_WAL_METERS
        .bytes_deleted
        .add(total_bytes, &[]);
    tracing::debug!(
        deleted_files = num_deleted_files,
        "Vacuum-segment-wal batch completed successfully"
    );

    Ok(VacuumSegmentWalBatchOutput {
        num_purged_entries,
        planned_num_purged_entries,
        planned_num_deleted_files: num_deleted_files,
        num_deleted_files,
    })
}
