"use client";

// This file is very similar to, but not exactly the same as, logs-viewer.tsx. This is the new approach going forward,
// so if you change it, you may need to change logs-viewer.tsx as well.
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useAnalytics } from "#/ui/use-analytics";
import { addClause, makeBubble, type Clause } from "#/utils/search/search";
import {
  type SetStateAction,
  type Dispatch,
  useCallback,
  useContext,
  useMemo,
  useRef,
  useState,
} from "react";
import { type SavingState } from "#/ui/saving";
import { doubleQuote } from "#/utils/sql-utils";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "#/ui/resizable";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { LogsTimeseriesInsight } from "#/ui/logs-timeseries-insight";
import { ObjectPermissionsDialog } from "#/app/app/[org]/p/[project]/permissions/object-permissions-dialog";
import { Skeleton } from "#/ui/skeleton";
import { type ViewType } from "@braintrust/typespecs";
import { type ViewParams } from "#/utils/view/use-view";
import {
  BUILT_IN_CUSTOM_COLUMNS,
  useCustomColumns,
} from "#/utils/custom-columns/use-custom-columns";
import { useGetRowsForExport } from "#/utils/data-object";
import columnReorderer from "#/app/app/[org]/p/[project]/experiments/[experiment]/table-column-reorderer";
import { usePanelSize } from "#/ui/use-panel-size";
import { type TraceViewParams } from "#/ui/trace/trace";
import { TableRowHeightToggle } from "#/ui/table-row-height-toggle";
import { useTraceFullscreen } from "#/ui/trace/use-trace-fullscreen";
import { TableSkeleton } from "#/ui/table/table-skeleton";
import Footer from "#/ui/landing/footer";
import { useSummaryPaginatedObjectViewerDataComponents } from "#/ui/summary-paginated-object-viewer";
import { type RowId } from "#/utils/diffs/diff-objects";
import {
  INITIALLY_VISIBLE_COLUMNS,
  usePaginatedObjectViewerVizComponents,
} from "#/ui/paginated-object-viewer";
import { type LogsPanelLayout } from "#/ui/logs-viewer";
import { DuckDBTypeHints } from "#/utils/schema";
import { useIsSidenavDocked } from "#/app/app/[org]/sidenav-state";
import { cn } from "#/utils/classnames";
import {
  BodyWrapper,
  HEIGHT_WITH_DOUBLE_TOP_OFFSET,
} from "#/app/app/body-wrapper";
import { LogsHeader } from "#/app/app/[org]/p/[project]/logs/logs-header";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { fetchBtql } from "#/utils/btql/btql";
import { useBtqlFlags, useIsFeatureEnabled } from "#/lib/feature-flags";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import { useQueryClient } from "@tanstack/react-query";
import { DockedChatSpacer } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/docked-chat-spacer";
import { OptimizationProvider } from "#/utils/optimization/provider";
import { GlobalChatProvider } from "./optimization/global-chat-provider";
import {
  canShowFilteredLogSummaries,
  useFilteredLogSummaries,
} from "./filtered-log-summaries";
import { pluralize } from "#/utils/plurals";
import { NULL_DASH } from "./table/formatters/null-formatter";
import { isEmpty } from "braintrust/util";
import { BT_ASSIGNMENTS, BT_ASSIGNMENTS_META_FIELD } from "#/utils/assign";
import {
  makeDefaultCustomMetric,
  type MetricDefinition,
} from "@braintrust/local/api-schema";
import { TimeRangeSelect } from "./time-range-select/time-range-select";
import { type Permission } from "@braintrust/typespecs";
import { TIME_RANGE_OPTIONS } from "#/app/app/[org]/monitor/time-controls/time-range";
import { type TimeRangeFilter as TimeRangeFilterType } from "#/utils/view/use-view";
import { format } from "date-fns";
import { LogsEmptyState } from "./logs-empty-state";
import { parseDateString } from "./time-range-select/parse-date-string";

function getTimeRangeLabel(
  timeRangeFilter: TimeRangeFilterType | undefined,
): string {
  if (!timeRangeFilter || timeRangeFilter === "all") {
    return "No logs found";
  }

  if (typeof timeRangeFilter === "string") {
    const timeRangeOption = [
      ...TIME_RANGE_OPTIONS,
      { value: "all", label: "All time", isLive: false },
    ].find((option) => option.value === timeRangeFilter);

    if (timeRangeOption) {
      return `No logs found in the past ${timeRangeOption.label.toLowerCase()}`;
    }

    return "No logs found in the specified time range";
  }

  if (
    typeof timeRangeFilter === "object" &&
    timeRangeFilter.from &&
    timeRangeFilter.to
  ) {
    const fromDate = parseDateString(timeRangeFilter.from);
    const toDate = parseDateString(timeRangeFilter.to);

    if (!Number.isNaN(fromDate.getTime()) && !Number.isNaN(toDate.getTime())) {
      return `No logs found from ${format(fromDate, "MMM d, yyyy")} to ${format(toDate, "MMM d, yyyy")}`;
    }
  }

  return "No logs found in the specified time range";
}

export default function SummaryLogsViewer({
  logType,
  logId,
  setSavingState,
  defaultPanelLayout,
  permissions,
}: {
  logType: "project_logs";
  logId: string | null;
  extraLeftControls?: React.ReactNode;
  setSavingState: Dispatch<SetStateAction<SavingState>>;
  defaultPanelLayout: LogsPanelLayout;
  permissions: Permission[];
}) {
  useAnalytics({
    page: {
      category: "logs",
      props: {
        project_id: logId,
      },
    },
  });

  const {
    orgName,
    projectId,
    projectName,
    config: { metricDefinitions },
  } = useContext(ProjectContext);

  const objectType = "project_logs";
  const viewType: ViewType | null = "logs";
  const viewParams: ViewParams | undefined = useMemo(
    () =>
      projectId && viewType
        ? {
            objectType: "project",
            objectId: projectId,
            viewType,
          }
        : undefined,
    [projectId],
  );

  const customColumnState = useCustomColumns({
    scope: projectId
      ? {
          object_type: "project",
          object_id: projectId,
          subtype: "project_log",
          variant: "project_log",
        }
      : undefined,
  });

  const [permissionsOpen, setPermissionsOpen] = useState(false);
  const [selectedBucket, setSelectedBucket] = useState<
    Clause<"filter"> | undefined
  >(undefined);

  const minSidePanelWidth = usePanelSize(640);
  const minMainPanelWidth = usePanelSize(400);

  const mainPanelScrollContainer = useRef<HTMLDivElement>(null);

  const traceViewParamArgs = useMemo(
    (): Partial<TraceViewParams> => ({
      title: "log",
    }),
    [],
  );

  const paginatedObjectViewerDataComponents =
    useSummaryPaginatedObjectViewerDataComponents({
      objectType: logType,
      objectId: logId,
      objectName: projectName,
      pageSize: 50,
      setSavingState,
      selectedBucket,
      traceViewParamArgs,
      viewParams,
      disableGrouping: true,
    });

  const {
    projectedPaths,
    filters,
    viewProps,
    initialQueryLoading,
    clauseChecker,
  } = paginatedObjectViewerDataComponents;

  const projectSummaryMetrics = useIsFeatureEnabled("projectSummaryMetrics");

  const extraLeftControls = useMemo(
    () => (
      <>
        <TableRowHeightToggle
          tableRowHeight={viewProps.rowHeight ?? "compact"}
          onSetRowHeight={viewProps.setRowHeight}
        />
      </>
    ),
    [viewProps.rowHeight, viewProps.setRowHeight],
  );

  const [rowIds, setRowIds] = useState<RowId[]>([]);

  const getRowsForExport = useGetRowsForExport({
    objectType,
    objectId: projectId,
    filters,
    exportedColumns: projectedPaths,
    // Only export the currently loaded data, since the logs page can be
    // infinitely long, but refetch to export non-truncated data.
    shouldRefetchObject: true,
    layout: viewProps.layout,
    dataObjectSearchParams: useMemo(
      () => ({
        shape: "summary",
        limit: paginatedObjectViewerDataComponents.tableQuery.data?.length,
        custom_columns: BUILT_IN_CUSTOM_COLUMNS["project_logs"]?.map(
          ({ name, expr }) => ({
            alias: name,
            expr: { btql: expr },
          }),
        ),
      }),
      [paginatedObjectViewerDataComponents.tableQuery.data],
    ),
  });

  const isReadOnly =
    !permissions.includes("update") && !permissions.includes("delete");

  const customMetrics = useMemo((): MetricDefinition[] => {
    const ignoreMetrics = new Set([
      // These should stay "special"
      "cached",
      "start",
      "end",
      // TODO: These should get folded into the metric definitions
      "duration",
      "llm_duration",
      "estimated_cost",
    ]);

    const metricNames =
      paginatedObjectViewerDataComponents.tableQuery.fields
        ?.find((f) => f.name === "metrics")
        ?.type.children.map((c: { name: string }) => c.name)
        .filter((name: string) => !ignoreMetrics.has(name)) ?? [];
    return metricNames.map(
      (name: string) =>
        metricDefinitions.find((def) => def.field_name === name) ??
        makeDefaultCustomMetric(name),
    );
  }, [
    metricDefinitions,
    paginatedObjectViewerDataComponents.tableQuery.fields,
  ]);

  const builder = useBtqlQueryBuilder({});
  const btqlFlags = useBtqlFlags();
  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();
  const queryClient = useQueryClient();
  const refetchTooltipContentData = useCallback(
    async (fieldName: string, rowId: string, previewLength?: number) => {
      if (!logId) return undefined;

      const queryKey = ["tooltip-content", logId, rowId, previewLength];
      const rowData = await queryClient.fetchQuery({
        queryKey,
        queryFn: async () => {
          const response = await fetchBtql({
            args: {
              query: {
                filter: builder.and({
                  btql: `id = '${rowId}'`,
                }),
                from: builder.from(logType, [logId], "summary"),
                select: [{ op: "star" }],
                custom_columns: [
                  {
                    alias: BT_ASSIGNMENTS,
                    expr: {
                      btql: `metadata.${doubleQuote(BT_ASSIGNMENTS_META_FIELD)}`,
                    },
                  },
                ],
                preview_length: previewLength ?? 1500,
              },
              brainstoreRealtime: true,
              disableLimit: false,
            },
            btqlFlags,
            apiUrl,
            getOrRefreshToken,
          });
          return Array.isArray(response.data) ? response.data[0] : {};
        },
        staleTime: 60 * 1000,
        gcTime: 5 * 60 * 1000,
      });

      return rowData?.[fieldName];
    },
    [
      apiUrl,
      builder,
      btqlFlags,
      logId,
      logType,
      getOrRefreshToken,
      queryClient,
    ],
  );

  const canShowSummaries = useMemo(
    () => canShowFilteredLogSummaries(filters?.btql ?? []),
    [filters],
  );

  const filteredLogSummaries = useFilteredLogSummaries({
    filters,
    objectType: "project_logs",
    objectId: canShowSummaries ? projectId : null,
    metricDefinitions: customMetrics,
    // NOTE: We can probably be smarter about this, within the Brainstore optimizer, but for now,
    // use spans if there are no custom filters, and traces otherwise.
    shape:
      Object.keys(paginatedObjectViewerDataComponents.viewProps.search)
        .length == 0
        ? "spans"
        : "traces",
  });

  const summarySlots = useMemo(() => {
    if (!canShowSummaries || !projectSummaryMetrics || !filteredLogSummaries) {
      return undefined;
    }
    const slots: Record<string, React.ReactNode> = {
      span_type_info: (
        <HeaderSummaryCell className="pt-0.5">
          {(filteredLogSummaries.traces ?? 0)?.toLocaleString()}{" "}
          {pluralize(filteredLogSummaries.traces ?? 0, "trace")}
        </HeaderSummaryCell>
      ),
      "metrics.duration": (
        <HeaderSummaryCell>
          {isEmpty(filteredLogSummaries.p50_latency) ? (
            NULL_DASH
          ) : (
            <>
              <span className="text-sm font-semibold text-primary-700">
                {filteredLogSummaries.p50_latency?.toLocaleString(undefined, {
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 3,
                })}
                s
              </span>{" "}
              <span className="text-[10px]">p50</span>
            </>
          )}
        </HeaderSummaryCell>
      ),
      "metrics.llm_duration": (
        <HeaderSummaryCell>
          {isEmpty(filteredLogSummaries.p50_llm_latency) ? (
            NULL_DASH
          ) : (
            <>
              <span className="text-sm font-semibold text-primary-700">
                {filteredLogSummaries.p50_llm_latency?.toLocaleString(
                  undefined,
                  {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 3,
                  },
                )}
                s
              </span>{" "}
              <span className="text-[10px]">p50</span>
            </>
          )}
        </HeaderSummaryCell>
      ),
      "metrics.estimated_cost": (
        <HeaderSummaryCell>
          {isEmpty(filteredLogSummaries.cost) ? (
            NULL_DASH
          ) : (
            <>
              <span className="text-sm font-semibold text-primary-700">
                {filteredLogSummaries.cost?.toLocaleString(undefined, {
                  style: "currency",
                  currency: "USD",
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 3,
                })}
              </span>{" "}
              <span className="text-[10px]">SUM</span>
            </>
          )}
        </HeaderSummaryCell>
      ),
      ...Object.fromEntries(
        customMetrics.map((def) => {
          const summaryResult: number | undefined | null =
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            filteredLogSummaries[
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
              def.field_name as keyof typeof filteredLogSummaries
            ] as number | undefined | null;
          return [
            `metrics.${def.field_name}`,
            <HeaderSummaryCell key={def.field_name}>
              {isEmpty(summaryResult) ? (
                NULL_DASH
              ) : (
                <>
                  <span className="text-sm font-semibold text-primary-700">
                    {summaryResult.toLocaleString(undefined, {
                      maximumFractionDigits: 2,
                    })}
                  </span>{" "}
                  <span className="text-[10px]">SUM</span>
                </>
              )}
            </HeaderSummaryCell>,
          ];
        }),
      ),
    };

    Object.entries(filteredLogSummaries.scores ?? {}).forEach(
      ([scoreName, scoreValue]) => {
        if (!isEmpty(scoreValue.avg)) {
          slots[`scores.${scoreName}`] = (
            <HeaderSummaryCell>
              <span className="text-sm font-semibold text-primary-700">
                {scoreValue.avg.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
                %
              </span>{" "}
              <span className="text-[10px]">AVG</span>
            </HeaderSummaryCell>
          );
        }
      },
    );

    return slots;
  }, [
    canShowSummaries,
    customMetrics,
    filteredLogSummaries,
    projectSummaryMetrics,
  ]);

  const vizQueryPropsOverride = useMemo(
    () => ({
      isHumanReviewModeEnabled: true,
      scrollContainerRef: mainPanelScrollContainer,
      multilineRow: viewProps.rowHeight === "tall" ? { numRows: 5 } : undefined,
      showRowNumber: true,
      columnReorderer,
      isSortable: false,
      typeHints: {
        ...DuckDBTypeHints[objectType],
        ...Object.fromEntries(
          customColumnState?.customColumnDefinitions?.map((c) => [
            c.name,
            "JSON",
          ]) ?? [],
        ),
      },
      isLoading: initialQueryLoading,
      disabledFilterColumns: ["comments"],
      refetchTooltipContentData,
      noRowsFoundLabel: getTimeRangeLabel(viewProps.timeRangeFilter),
      summarySlots,
      afterToolbarSlot: projectSummaryMetrics && canShowSummaries && (
        <div className="flex h-20 w-full flex-none flex-col">
          <LogsTimeseriesInsight
            projectId={projectId ?? ""}
            filters={filters}
            onBucketClick={setSelectedBucket}
            timeRangeFilter={viewProps.timeRangeFilter}
            setTimeRangeFilter={viewProps.setTimeRangeFilter}
          />
        </div>
      ),
      filterSuggestions: [
        {
          label: "Duration > 3s",
          btql: "metrics.duration > 3000",
        },
        {
          label: "Cost > $1",
          btql: "metrics.estimated_cost > 1",
        },
      ],
    }),
    [
      canShowSummaries,
      viewProps.rowHeight,
      customColumnState.customColumnDefinitions,
      objectType,
      initialQueryLoading,
      refetchTooltipContentData,
      summarySlots,
      projectSummaryMetrics,
      filters,
      projectId,
      viewProps.timeRangeFilter,
      viewProps.setTimeRangeFilter,
    ],
  );

  // https://github.com/bvaughn/react-resizable-panels?tab=readme-ov-file#how-can-i-use-persistent-layouts-with-ssr
  const onPanelLayout = useCallback(
    (sizes: number[]) => {
      const layoutCookie: LogsPanelLayout = {
        ...defaultPanelLayout,
        main: sizes[0],
        trace: sizes[1],
      };
      document.cookie = `react-resizable-panels:logs2-layout=${JSON.stringify(
        layoutCookie,
      )}; path=/`;
    },
    [defaultPanelLayout],
  );

  const queryError =
    paginatedObjectViewerDataComponents.tableQuery.queryErrors?.find(Boolean);
  const paginatedObjectViewerObjectVizComponents =
    usePaginatedObjectViewerVizComponents(
      {
        ...paginatedObjectViewerDataComponents,
      },
      {
        rowIds,
        setRowIds,
        aiSearchType: "logs",
        includeExperimentSelectionSection: true,
        isLogsPage: true,
        extraLeftControls,
        extraRightControls: (
          <TimeRangeSelect
            context="logs"
            value={viewProps.timeRangeFilter}
            setValue={viewProps.setTimeRangeFilter}
            allowAllTime
          />
        ),
        vizQueryPropsOverride,
        customError: queryError ? <>{queryError.message}</> : undefined,
        totalCount: filteredLogSummaries?.traces ?? undefined,
        showGrouping: true,
      },
    );

  const isSidenavDocked = useIsSidenavDocked();

  const { isTraceFullscreen } = useTraceFullscreen();

  const columnVisibility = useMemo(() => {
    return {
      ...INITIALLY_VISIBLE_COLUMNS,
      ...viewProps.columnVisibility,
    };
  }, [viewProps.columnVisibility]);

  const header = (
    <LogsHeader
      projectId={projectId ?? ""}
      projectName={projectName}
      orgName={orgName}
      getRowsForExport={getRowsForExport}
      columnVisibility={columnVisibility}
      onBTQLFilter={async (filterText) => {
        const clauseSpec = { type: "filter" as const, text: filterText };
        const checked = await clauseChecker?.(clauseSpec);
        const clause = {
          ...clauseSpec,
          ...(checked?.type === "checked"
            ? checked.extraFields
            : { btql: { parsed: { btql: filterText } } }),
          bubble: makeBubble({
            clause: clauseSpec,
            setSearch: viewProps.setSearch,
          }),
        } as const;
        viewProps.setSearch(() => addClause({}, clause));
      }}
      isReadOnly={isReadOnly}
    />
  );

  if (paginatedObjectViewerObjectVizComponents.status === "loading_initial") {
    return (
      <div>
        <LogsHeader
          projectId={projectId ?? ""}
          projectName={projectName}
          orgName={orgName}
          getRowsForExport={getRowsForExport}
          hideActions
          columnVisibility={columnVisibility}
          isReadOnly={isReadOnly}
        />
        <BodyWrapper outerClassName="flex-1">
          <MainContentWrapper>
            <Skeleton className="mb-2 h-[200px]" />
            <TableSkeleton />
          </MainContentWrapper>
        </BodyWrapper>
        <DockedChatSpacer />
      </div>
    );
  } else if (
    paginatedObjectViewerObjectVizComponents.status === "loaded_empty"
  ) {
    return (
      <div>
        <LogsHeader
          projectId={projectId ?? ""}
          projectName={projectName}
          orgName={orgName}
          getRowsForExport={getRowsForExport}
          hideActions
          columnVisibility={columnVisibility}
          isReadOnly={isReadOnly}
        />
        <BodyWrapper
          outerClassName={cn(HEIGHT_WITH_DOUBLE_TOP_OFFSET, "flex-1")}
          innerClassName="overflow-auto"
        >
          <MainContentWrapper className="[&>footer]:pb-4 [&>footer]:sm:pb-4 [&>footer]:lg:pb-4">
            <LogsEmptyState
              orgName={orgName}
              projectName={projectName}
              projectId={projectId ?? undefined}
            />
          </MainContentWrapper>
        </BodyWrapper>
        <DockedChatSpacer />
      </div>
    );
  }

  const { tableViewComponents, tracePanelComponents } =
    paginatedObjectViewerObjectVizComponents;

  if (isTraceFullscreen && tracePanelComponents) {
    return (
      <div>
        {header}
        <BodyWrapper
          outerClassName="flex-1"
          innerClassName="flex overflow-hidden flex-1"
        >
          {tracePanelComponents}
        </BodyWrapper>
        <DockedChatSpacer />
      </div>
    );
  }

  return (
    <OptimizationProvider
      queryProjectId={projectId ?? undefined}
      timeRangeFilter={viewProps.timeRangeFilter}
    >
      <GlobalChatProvider isDefaultOpen={true}>
        <div className={cn("flex overflow-hidden h-full")}>
          <div className="flex min-w-0 flex-1 flex-col">
            {header}
            <BodyWrapper
              innerClassName="flex"
              outerClassName={HEIGHT_WITH_DOUBLE_TOP_OFFSET}
            >
              <ResizablePanelGroup
                direction="horizontal"
                autoSaveId="logsPanelLayout"
                className="flex !flex-row-reverse overflow-hidden"
                onLayout={onPanelLayout}
              >
                {tracePanelComponents && (
                  <>
                    <ResizablePanel
                      order={1}
                      defaultSize={Math.max(
                        defaultPanelLayout.trace,
                        minSidePanelWidth,
                      )}
                      minSize={minSidePanelWidth}
                      id="trace"
                      className="flex flex-col overflow-hidden"
                    >
                      {tracePanelComponents}
                    </ResizablePanel>
                    <ResizableHandle />
                  </>
                )}
                <ResizablePanel
                  order={0}
                  minSize={minMainPanelWidth}
                  defaultSize={defaultPanelLayout.main}
                  id="main"
                  className="relative"
                >
                  <div
                    className={cn(
                      "flex flex-col overflow-auto px-3 pb-3 rounded-tl-md",
                      HEIGHT_WITH_DOUBLE_TOP_OFFSET,
                      {
                        "rounded-tl-none": !isSidenavDocked,
                      },
                    )}
                    ref={mainPanelScrollContainer}
                  >
                    <ObjectPermissionsDialog
                      objectType="project_log"
                      objectId={projectId ?? ""}
                      objectName={projectName}
                      orgName={orgName}
                      projectName={projectName}
                      open={permissionsOpen}
                      onOpenChange={setPermissionsOpen}
                    />
                    {tableViewComponents}
                    <div className="grow" />
                    <Footer
                      className="sticky left-0 w-full pb-2 sm:pb-2 lg:pb-2"
                      inApp
                      orgName={orgName}
                    />
                  </div>
                </ResizablePanel>
              </ResizablePanelGroup>
            </BodyWrapper>
          </div>
          <DockedChatSpacer />
        </div>
      </GlobalChatProvider>
    </OptimizationProvider>
  );
}

const HeaderSummaryCell = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        "mt-1 truncate text-xs text-primary-400 pl-3 font-normal w-full",
        className,
      )}
    >
      {children}
    </div>
  );
};
