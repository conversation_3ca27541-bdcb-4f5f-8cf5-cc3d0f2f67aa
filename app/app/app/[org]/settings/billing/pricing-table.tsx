import Link from "next/link";
import { getOrgSettingsLink } from "#/app/app/[org]/getOrgLink";
import { PLAN_SLUGS } from "./plans";
import { buttonVariants } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { ArrowRight } from "lucide-react";
import { useAppAnalytics } from "#/ui/analytics/segment-analytics";
import { useOrg } from "#/utils/user";
import { getPageName } from "#/utils/analytics";

export const PricingTable = ({
  isLanding,
  orgName,
}: {
  isLanding?: boolean;
  orgName?: string;
}) => {
  const { track } = useAppAnalytics();
  const org = useOrg();
  const settingsLink = getOrgSettingsLink({ orgName: orgName ?? "" });

  return (
    <div className="flex flex-col gap-4 lg:flex-row">
      <div className="flex flex-1 gap-2">
        <div
          className={cn("flex-1 rounded-lg p-6 bg-primary-200", {
            "bg-white text-black": isLanding,
          })}
        >
          <Plan isLanding={isLanding}>Free</Plan>
          <div className="border-b pb-6 text-4xl border-black">
            $0{" "}
            <span
              className={cn({
                "text-lg": isLanding,
              })}
            >
              / month
            </span>
          </div>
          <Feature
            isLanding={isLanding}
            title="1 million"
            description="Trace spans"
            className="mt-7"
          />
          <Feature
            isLanding={isLanding}
            title="1 GB"
            description="Processed data"
            className="mt-4"
          />
          <Feature
            isLanding={isLanding}
            title="10,000"
            description="Scores and custom metrics"
            className="mt-4"
          />
          <Feature
            isLanding={isLanding}
            title="14 days"
            description="Data retention"
            className="mt-4"
          />
          <Feature
            isLanding={isLanding}
            title="Unlimited"
            description="Users"
            className="mt-4"
          />
          {isLanding && (
            <Link
              href="/signup"
              className={cn(
                buttonVariants({ variant: "inverted" }),
                "mt-8 text-lg font-medium text-white bg-black",
              )}
            >
              Get started for free
            </Link>
          )}
        </div>
      </div>
      <div className="flex flex-1">
        <div
          className={cn("flex-1 rounded-lg p-6 text-background", {
            "bg-brandGreen text-brandForest": isLanding,
            "bg-accent-600": !isLanding,
          })}
        >
          <Plan isLanding={isLanding}>Pro</Plan>
          <div
            className={cn("border-b pb-6 text-4xl", {
              "border-black": isLanding,
            })}
          >
            $249{" "}
            <span
              className={cn({
                "text-lg": isLanding,
              })}
            >
              / month
            </span>
          </div>
          <Feature
            isLanding={isLanding}
            title="Unlimited"
            description="Trace spans"
            className="mt-7"
          />
          <Feature
            isLanding={isLanding}
            title="5 GB"
            description="Processed data ($3/GB thereafter)"
            className="mt-4"
          />
          <Feature
            isLanding={isLanding}
            title="50,000"
            description="Scores and custom metrics ($1.50/1,000 thereafter)"
            className="mt-4"
          />
          <Feature
            isLanding={isLanding}
            title="1 month"
            description="Data retention ($3/GB retained thereafter)"
          />
          <Feature
            isLanding={isLanding}
            title="Unlimited"
            description="Users"
          />
          {!isLanding && (
            <Link
              href={`${settingsLink}/billing/payment?purchasePlanSlug=${PLAN_SLUGS.PRO}`}
              className={cn(
                buttonVariants({ variant: "default" }),
                "mt-8 font-medium text-primary-900",
              )}
              onClick={() => {
                track("upgradeClick", {
                  orgName: orgName ?? "",
                  orgId: org?.id ?? "",
                  entryPoint: "pricingTable",
                  destination: "paymentPage",
                  destinationUrl: `${settingsLink}/billing/payment?purchasePlanSlug=${PLAN_SLUGS.PRO}`,
                  context: "pro_plan",
                  sourcePage: getPageName(),
                });
              }}
            >
              Upgrade <ArrowRight className="size-4" />
            </Link>
          )}
          {isLanding && (
            <Link
              href="/signup"
              className={cn(
                buttonVariants({ variant: "inverted" }),
                "mt-8 text-lg font-medium text-white bg-black",
              )}
            >
              Get started
            </Link>
          )}
        </div>
      </div>
      <div className="flex flex-1">
        <div
          className={cn(
            "flex flex-1 flex-col items-start rounded-lg p-6 text-background bg-primary-950",
            {
              "bg-brandBlue text-brandBlue3": isLanding,
            },
          )}
        >
          <Plan isLanding={isLanding}>Enterprise</Plan>
          <div
            className={cn("w-full border-b pb-6 text-4xl border-white", {
              "border-brandBlue3": isLanding,
            })}
          >
            Custom
          </div>
          <div className="mt-7 flex-1 text-pretty text-lg opacity-80">
            Premium support with on-prem or hosted deployment for high volume or
            privacy-sensitive data.
          </div>
          <Link
            href="/contact"
            className={cn(
              buttonVariants({ variant: "default" }),
              "mt-8 font-medium text-primary-900",
              {
                "text-lg text-white bg-black hover:bg-black": isLanding,
              },
            )}
          >
            Contact us
          </Link>
        </div>
      </div>
    </div>
  );
};

const Plan = ({
  children,
  className,
  isLanding,
}: {
  children: React.ReactNode;
  className?: string;
  isLanding?: boolean;
}) => {
  return (
    <h2
      className={cn(
        "text-xl font-semibold uppercase mb-4",
        {
          "font-suisse tracking-wider font-normal text-base": isLanding,
        },
        className,
      )}
    >
      {children}
    </h2>
  );
};

const Feature = ({
  title,
  description,
  className,
  isLanding,
}: {
  title: string;
  description: string;
  className?: string;
  isLanding?: boolean;
}) => (
  <div className={cn("mt-4", className)}>
    <div className="text-xl font-medium">{title}</div>
    <div
      className={cn("text-sm opacity-60", {
        "text-base opacity-100": isLanding,
      })}
    >
      {description}
    </div>
  </div>
);
