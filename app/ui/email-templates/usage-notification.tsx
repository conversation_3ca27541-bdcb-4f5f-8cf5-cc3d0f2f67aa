import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON>er,
  Head,
  <PERSON><PERSON>,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import { Tailwind } from "@react-email/tailwind";

interface UsageNotificationProps {
  usageType: "scores_and_custom_metrics" | "log_bytes";
  percentUsed: 80 | 90 | 100;
  organizationName: string;
  baseImgUrl?: string;
  baseAppUrl?: string;
}

const DEFAULT_BASE_URL =
  process.env.NEXT_PUBLIC_SITE_URL ?? "https://braintrust.dev";

const USAGE_QUOTAS = {
  log_bytes: { label: "1 GB processed data" },
  scores_and_custom_metrics: { label: "10,000 score" },
};

function getLimitMessage({
  percentUsed,
  usageType,
  organizationName,
}: {
  percentUsed: 80 | 90 | 100;
  usageType: "scores_and_custom_metrics" | "log_bytes";
  organizationName: string;
}) {
  const quotaLabel = USAGE_QUOTAS[usageType].label;
  if (percentUsed === 100) {
    return `You've reached ${percentUsed}% of your ${quotaLabel} allowance on the Free plan for ${organizationName}.`;
  }

  return `You're currently at ${percentUsed}% of your monthly ${quotaLabel} allowance for ${organizationName}.`;
}

function getConsequenceMessage(percentUsed: 80 | 90 | 100) {
  if (percentUsed === 100) {
    return "As a result, you can no longer run evals or log any new data.";
  }

  return "Once you reach 100%, your logs, experiments, and playground runs will no longer be captured.";
}

function getSolutionMessage(percentUsed: 80 | 90 | 100) {
  if (percentUsed === 100) {
    return "Upgrade to the Pro plan now to immediately restore access, prevent data loss, and continue running evals without interruption. You’ll also get:";
  }

  return "To ensure uninterrupted access and avoid losing valuable data, upgrade to the Pro plan. You’ll get:";
}

function getLinks(baseAppUrl: string, orgNameEncoded: string) {
  return {
    usage: `${baseAppUrl}/app/${orgNameEncoded}/settings/billing?source=email&entryPoint=usageWarning&destination=billingPage`,
    upgrade: `${baseAppUrl}/app/${orgNameEncoded}/settings/billing/payment?purchasePlanSlug=pro&source=email&entryPoint=usageWarning&destination=paymentPage`,
  };
}

const UsageNotification = ({
  usageType,
  percentUsed,
  organizationName,
  baseImgUrl = DEFAULT_BASE_URL,
  baseAppUrl = DEFAULT_BASE_URL,
}: UsageNotificationProps) => {
  const limitMessage = getLimitMessage({
    percentUsed,
    usageType,
    organizationName,
  });
  const consequenceMessage = getConsequenceMessage(percentUsed);
  const solutionMessage = getSolutionMessage(percentUsed);

  const { usage, upgrade } = getLinks(
    baseAppUrl,
    encodeURIComponent(organizationName),
  );

  return (
    <Html>
      <Head />
      <Preview>{limitMessage}</Preview>
      <Tailwind>
        <Body className="m-auto p-4 font-sans">
          <Container className="max-w-[520px] pt-8 bg-white">
            <Section>
              <Img
                src={`${baseImgUrl}/logo-letter.png`}
                width="100"
                alt="Braintrust"
                className="my-0"
              />
            </Section>
            <Heading className="mx-0 my-[30px] p-0 text-xl font-medium text-black">
              {limitMessage}
            </Heading>
            <Text className="text-base text-black">{consequenceMessage}</Text>
            <Text className="text-base text-black">{solutionMessage}</Text>
            <Section className="text-base">
              <ul className="pl-6">
                <li className="mb-3">
                  <span className="font-medium">5 GB</span> monthly processed
                  data
                </li>
                <li className="mb-3">
                  <span className="font-medium">Unlimited</span> trace spans
                </li>
                <li className="mb-3">
                  <span className="font-medium">50,000</span> scores/month
                </li>
                <li className="mb-3">
                  <span className="font-medium">Extended 1-month</span> data
                  retention
                </li>
              </ul>
            </Section>
            <Section className="my-[32px]">
              <Button
                className="mr-2 rounded px-4 py-2 text-sm font-medium no-underline bg-[#3b82f6] text-white"
                href={upgrade}
              >
                Upgrade
              </Button>
              <Button
                className="rounded border border-solid px-4 py-2 text-sm font-medium no-underline bg-white border-zinc-300 text-black"
                href={usage}
              >
                View usage
              </Button>
            </Section>
            <div className="h-px bg-zinc-200/80" />
            <Text className="text-sm text-black">
              If you have any questions, please contact{" "}
              <Link href="mailto:<EMAIL>">
                <EMAIL>
              </Link>
              .
            </Text>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

const previewProps: UsageNotificationProps = {
  usageType: "scores_and_custom_metrics",
  percentUsed: 80,
  organizationName: "Example Org",
  baseImgUrl: "https://braintrust.dev",
  baseAppUrl: "http://localhost:3000",
};

UsageNotification.PreviewProps = previewProps;

export default UsageNotification;
