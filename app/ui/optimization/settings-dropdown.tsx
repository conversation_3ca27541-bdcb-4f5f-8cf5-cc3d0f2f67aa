import { Settings2 } from "lucide-react";
import { But<PERSON> } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuGroup,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
} from "#/ui/dropdown-menu";
import { type ToolName } from "@braintrust/local/optimization/tools";
import { INITIAL_LOOP_TOOLS } from "#/ui/optimization/global-chat-provider";
import { BasicTooltip } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import { type TimeRangeFilter as TimeRangeFilterType } from "#/utils/view/use-view";
import { TimeRangeSelectItem } from "#/ui/time-range-select/time-range-select";
import { addMinutes, endOfDay, startOfDay } from "date-fns";
import { Calendar } from "#/ui/calendar";
import {
  ALL_TIME_RANGE,
  getTimeRangeOptions,
  type TimeRangeOption,
} from "#/ui/time-range-select/get-time-range-options";
import { useCallback, useMemo } from "react";
import { type DateRange } from "react-day-picker";
import { UTCDate } from "@date-fns/utc";
import { TimeRangePill } from "#/ui/time-range-select/time-range-pill";
import { getTimeRangeCustomString } from "#/ui/time-range-select/get-time-range-custom-string";
import { DEFAULT_TIME_RANGE } from "#/app/app/[org]/monitor/time-controls/time-range";
import { parseDateString } from "#/ui/time-range-select/parse-date-string";

export const SettingsDropdown = ({
  allowRunningWithoutConsent,
  setAllowRunningWithoutConsent,
  currentTool = INITIAL_LOOP_TOOLS,
  setCurrentTool,
  implementedTools,
  size,
  timeRangeSettings,
  setTimeRangeSettings,
  pageKey,
}: {
  allowRunningWithoutConsent: boolean;
  setAllowRunningWithoutConsent: (consent: boolean) => void;
  currentTool?: ToolName[];
  setCurrentTool: (
    tools: ToolName[] | ((prev: ToolName[]) => ToolName[]),
  ) => void;
  implementedTools: ToolName[];
  size?: "widget" | "full";
  timeRangeSettings: TimeRangeFilterType;
  setTimeRangeSettings: (timeRange: TimeRangeFilterType) => void;
  pageKey: string;
}) => {
  const availableOptions = getTimeRangeOptions({ allowAllTime: true });
  const isCustom = typeof timeRangeSettings === "object";

  const calOnSelect = useCallback(
    (dateRange?: DateRange) => {
      if (!dateRange) {
        return;
      }
      const { from, to } = dateRange;

      if (!from) {
        return;
      }

      const offset = 0;

      const fromDate = new Date(from);
      const fromStartOfDay = startOfDay(
        addMinutes(fromDate, offset),
      ).toISOString();

      const toDate = new Date(to ?? from);
      const toEndOfDay = endOfDay(addMinutes(toDate, offset)).toISOString();

      setTimeRangeSettings({ from: fromStartOfDay, to: toEndOfDay });
    },
    [setTimeRangeSettings],
  );

  const calSelectedValue = useMemo(() => {
    if (!isCustom) {
      return undefined;
    }

    const offset = 0;

    const fromDate = parseDateString(timeRangeSettings.from);
    const toDate = parseDateString(timeRangeSettings.to);

    const fromStartDay = startOfDay(new UTCDate(fromDate));
    const toEndDay = endOfDay(new UTCDate(toDate));

    return {
      from: addMinutes(new Date(fromStartDay), offset),
      to: addMinutes(new Date(toEndDay), offset),
    };
  }, [isCustom, timeRangeSettings]);

  const toolLabels: Record<ToolName, string> = {
    get_summary: "Get summarized results",
    get_results: "Get detailed results",
    edit_task: "Edit prompt",
    run_task: "Run eval",
    edit_data: "Edit data",
    continue_execution: "Continue execution",
    get_available_scorers: "Get scorers",
    edit_scorers: "Edit scorers",
    create_llm_scorer: "Create LLM judge scorer",
    create_code_scorer: "Create code scorer",
    infer_schema: "Infer schema",
    btql_query: "BTQL query",
    search_docs: "Search docs",
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={cn("size-6 text-primary-500", size === "full" && "size-7")}
          iconClassName={cn("size-3", size === "full" && "size-3.5")}
          Icon={Settings2}
        />
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="flex w-fit flex-col gap-0 overflow-hidden"
      >
        <DropdownMenuLabel>Available tools</DropdownMenuLabel>
        <DropdownMenuGroup>
          {implementedTools
            .filter((tool) => tool !== "continue_execution")
            .map((tool) => (
              <DropdownMenuCheckboxItem
                key={tool}
                checked={currentTool.includes(tool)}
                onSelect={(e) => e.preventDefault()}
                onCheckedChange={(checked) => {
                  setCurrentTool(
                    checked
                      ? [...currentTool, tool]
                      : currentTool.filter((t: ToolName) => t !== tool),
                  );
                }}
              >
                {toolLabels[tool] ?? tool}
              </DropdownMenuCheckboxItem>
            ))}
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuLabel>Settings</DropdownMenuLabel>
        <BasicTooltip tooltipContent="Automatically accept the changes without asking for confirmation">
          <DropdownMenuCheckboxItem
            onSelect={(e) => e.preventDefault()}
            checked={allowRunningWithoutConsent}
            onCheckedChange={(checked) => {
              setAllowRunningWithoutConsent(checked);
            }}
          >
            Auto-accept edits
          </DropdownMenuCheckboxItem>
        </BasicTooltip>
        <DropdownMenuSeparator />
        <BasicTooltip tooltipContent="This determines the time range of the logs Loop can query.">
          <DropdownMenuLabel>Logs time range</DropdownMenuLabel>
        </BasicTooltip>
        {pageKey !== "logs" ? (
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <TimeRangeTriggerButtonContent
                timeRangeSettings={timeRangeSettings}
                availableOptions={availableOptions}
              />
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              {availableOptions.map((option) => (
                <TimeRangeSelectItem
                  key={option.value}
                  option={option}
                  isUTC={false}
                  isLogsContext={true}
                  isFreeOrg={false}
                  allowAllTime={true}
                  restrictLookback={false}
                  onSelect={(value) => setTimeRangeSettings(value)}
                />
              ))}
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>Custom</DropdownMenuSubTrigger>
                <DropdownMenuSubContent>
                  <Calendar
                    mode="range"
                    numberOfMonths={2}
                    selected={calSelectedValue}
                    onSelect={calOnSelect}
                    disabled={{
                      before: undefined,
                      after: endOfDay(new Date()),
                    }}
                  />
                </DropdownMenuSubContent>
              </DropdownMenuSub>
            </DropdownMenuSubContent>
          </DropdownMenuSub>
        ) : (
          <div className="flex max-w-[224px] gap-1 px-2 pb-1 text-xs text-primary-700">
            Loop will query using the configured logs page time range
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const TimeRangeTriggerButtonContent = ({
  timeRangeSettings,
  availableOptions,
}: {
  timeRangeSettings: TimeRangeFilterType;
  availableOptions: TimeRangeOption[];
}) => {
  const isCustom = typeof timeRangeSettings === "object";

  return isCustom ? (
    getTimeRangeCustomString({ value: timeRangeSettings, isUTC: false })
  ) : availableOptions.find((r) => r.value === timeRangeSettings) ? (
    <>
      <TimeRangePill isLive={false}>
        {availableOptions.find((r) => r.value === timeRangeSettings)?.value ===
        ALL_TIME_RANGE
          ? "All"
          : availableOptions.find((r) => r.value === timeRangeSettings)?.value}
      </TimeRangePill>
      {availableOptions.find((r) => r.value === timeRangeSettings)?.label ===
      "All time"
        ? ""
        : "Past "}
      {availableOptions.find((r) => r.value === timeRangeSettings)?.label}
    </>
  ) : (
    <>
      <TimeRangePill isLive={false}>{DEFAULT_TIME_RANGE!.value}</TimeRangePill>
      Past {DEFAULT_TIME_RANGE!.label}
    </>
  );
};
