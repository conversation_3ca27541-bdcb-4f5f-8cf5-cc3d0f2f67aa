import { <PERSON><PERSON> } from "#/ui/button";
import { type PromptData } from "#/ui/prompts/schema";
import { type TransactionId } from "braintrust/util";
import { useCallback, useMemo, useState } from "react";
import React from "react";
import { ExternalLink } from "#/ui/link";
import {
  type DataEditorCopilotContextFn,
  DataTextEditor,
} from "#/ui/data-text-editor";
import { type CopilotContextFormat } from "@braintrust/local/copilot";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { useFeatureFlags } from "#/lib/feature-flags";
import { DialogFooter } from "#/ui/dialog";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
} from "#/ui/form";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { ToolsDropdown } from "../../../prompt/[prompt]/tools/tools-dropdown";
import { zodErrorToString } from "#/utils/validation";
import { type OpenAIModelParams } from "@braintrust/typespecs";
import {
  ToolChoiceDropdownMenu,
  toolsChoiceDescription,
} from "../../../prompt/[prompt]/model-parameters";
import { Input } from "#/ui/input";
import { jsonSchemaObjectSchema } from "@braintrust/btql/schema";
import { cn } from "#/utils/classnames";

export type SelectedTools = PromptData["tool_functions"];

const openAIToolSchema = z.object({
  type: z.literal("function"),
  function: z.object({
    name: z.string(),
    description: z.string().optional(),
    parameters: jsonSchemaObjectSchema.optional(),
    strict: z.boolean().optional(),
  }),
});

const toolFunctionsSchema = z.object({
  selectedTools: z
    .array(
      z.object({
        type: z.literal("function"),
        id: z.string(),
      }),
    )
    .default([]),
  toolChoice: z
    .union([
      z.literal("auto"),
      z.literal("none"),
      z.literal("required"),
      z.object({
        type: z.literal("function"),
        function: z.object({ name: z.string() }),
      }),
    ])
    .optional(),
  rawTools: z.array(openAIToolSchema).nullish(),
});

type ToolsFormData = z.infer<typeof toolFunctionsSchema>;

const SAMPLE: z.infer<typeof openAIToolSchema>[] = [
  {
    type: "function",
    function: {
      description: "Returns the sum of two numbers.",
      name: "add",
      parameters: {
        type: "object",
        properties: {
          a: {
            type: "number",
            description: "The first number",
          },
          b: {
            type: "number",
            description: "The second number",
          },
        },
        required: ["a", "b"],
      },
    },
  },
];

interface ToolsFormProps {
  selectedTools: SelectedTools;
  onSave: (
    tools: SelectedTools,
    rawTools: Record<string, unknown>[] | null,
    toolChoice: OpenAIModelParams["tool_choice"],
  ) => Promise<TransactionId | null>;
  copilotContext?: CopilotContextBuilder;
  initialRawToolsValue: Record<string, unknown>[] | null;
  onClose: () => void;
  toolChoice: OpenAIModelParams["tool_choice"];
  isReadOnly?: boolean;
}

export const ToolsForm = ({
  selectedTools: selectedToolsProp,
  onSave,
  copilotContext,
  initialRawToolsValue,
  onClose,
  toolChoice,
  isReadOnly,
}: ToolsFormProps) => {
  const toolsAutocomplete: DataEditorCopilotContextFn = useCallback(
    (fmt: CopilotContextFormat) =>
      copilotContext?.makeCopilotContext({ type: "tools", fmt }),
    [copilotContext],
  );

  const defaultValues = useMemo(() => {
    return {
      selectedTools:
        selectedToolsProp?.filter(
          (t): t is { type: "function"; id: string } => t.type === "function",
        ) ?? [],
      rawTools: initialRawToolsValue,
      toolChoice,
    };
  }, [selectedToolsProp, initialRawToolsValue, toolChoice]);

  const form = useForm<ToolsFormData>({
    resolver: zodResolver(toolFunctionsSchema),
    defaultValues,
  });

  const {
    flags: { functionTools },
  } = useFeatureFlags();

  const onSubmit = async (data: ToolsFormData) => {
    await onSave(data.selectedTools, data.rawTools ?? null, data.toolChoice);
    onClose();
  };

  const rawToolsValue = form.watch("rawTools");
  const areRawToolsEmpty = !rawToolsValue;
  const [replaceRawToolsDialogOpen, setReplaceRawToolsDialogOpen] =
    useState(false);

  const selectedToolsValue = form.watch("selectedTools");
  const areSelectedToolsEmpty = selectedToolsValue.length === 0;
  const showSelectedToolsSection =
    functionTools && !(isReadOnly && areSelectedToolsEmpty);

  const rawToolsErrors = useMemo(() => {
    const result = z
      .array(openAIToolSchema)
      .nullable()
      .optional()
      .safeParse(rawToolsValue);
    if (result.success) return null;
    return zodErrorToString(new z.ZodError(result.error.issues), 2, true);
  }, [rawToolsValue]);

  const watchedToolChoice = form.watch("toolChoice");
  const showCustomToolChoice =
    !!watchedToolChoice &&
    typeof watchedToolChoice !== "string" &&
    !!watchedToolChoice.function;

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-1 flex-col overflow-hidden"
      >
        <div className="flex flex-1 flex-col overflow-auto p-4">
          {showSelectedToolsSection && (
            <>
              <div className="mb-2 text-xs font-normal">
                Tool functions from library
              </div>
              <FormField
                control={form.control}
                name="selectedTools"
                render={({ field }) => (
                  <FormItem>
                    <FormControl className="w-full">
                      <ToolsDropdown
                        isReadOnly={isReadOnly}
                        selectedTools={field.value}
                        onSelectTool={(tool) => {
                          field.onChange(
                            field.value.find(
                              (v) => v.type === "function" && v.id === tool.id,
                            )
                              ? field.value.filter(
                                  (v) =>
                                    v.type === "function" && v.id !== tool.id,
                                )
                              : [
                                  ...(field.value ?? []),
                                  {
                                    type: "function" as const,
                                    id: tool.id,
                                  },
                                ],
                            {
                              shouldDirty: true,
                              shouldTouch: true,
                            },
                          );
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}
          <div
            className={cn(
              "mb-2 mt-6 flex gap-3 text-xs font-normal",
              !showSelectedToolsSection && "mt-0",
            )}
          >
            Raw array of tools
            <div className="grow" />
            {!areRawToolsEmpty && !isReadOnly && (
              <Button
                size="inline"
                transparent
                className="text-xs font-normal text-primary-400"
                onClick={() => {
                  form.setValue("rawTools", null, {
                    shouldDirty: true,
                    shouldTouch: true,
                  });
                }}
              >
                Clear
              </Button>
            )}
            {!isReadOnly && (
              <Button
                type="button"
                size="inline"
                transparent
                className="text-xs font-normal text-accent-600"
                onClick={(e) => {
                  e.preventDefault();
                  if (!areRawToolsEmpty) {
                    setReplaceRawToolsDialogOpen(true);
                  } else {
                    form.setValue("rawTools", SAMPLE, {
                      shouldDirty: true,
                      shouldTouch: true,
                    });
                  }
                }}
              >
                Insert example
              </Button>
            )}
          </div>
          <ConfirmationDialog
            title="Replace raw tools"
            description="Are you sure you want to replace the current raw tools with an example?"
            confirmText="Replace"
            open={replaceRawToolsDialogOpen}
            onOpenChange={setReplaceRawToolsDialogOpen}
            onConfirm={() => {
              form.setValue("rawTools", SAMPLE, {
                shouldDirty: true,
                shouldTouch: true,
              });
            }}
          />
          <FormField
            control={form.control}
            name="rawTools"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <DataTextEditor
                    className="flex grow rounded-md text-sm"
                    forceError={!!form.formState.errors.rawTools}
                    autoFocus
                    value={field.value}
                    placeholder="An array of JSON-schema tools the model may call. Currently, only functions are supported as a tool."
                    formatOnBlur
                    allowedRenderOptions={["json"]}
                    makeCopilotContext={toolsAutocomplete}
                    readOnly={isReadOnly}
                    onChange={(value) => {
                      field.onChange(value, {
                        shouldDirty: true,
                        shouldTouch: true,
                      });
                    }}
                  />
                </FormControl>
                {rawToolsErrors && (
                  <div className="text-xs font-medium text-bad-700">
                    {rawToolsErrors}
                  </div>
                )}

                <FormDescription>
                  This JSON object corresponds to the{" "}
                  <code className="font-semibold">tools</code> argument in the{" "}
                  <ExternalLink href="https://platform.openai.com/docs/guides/function-calling?api-mode=chat">
                    OpenAI API
                  </ExternalLink>
                  . Functions that are defined by you, enabling the model to
                  call your own code. Currently, functions are the only
                  supported tool type.
                </FormDescription>
              </FormItem>
            )}
          />

          <div className="mb-2 mt-6 text-xs font-normal">Tool choice</div>
          <FormField
            control={form.control}
            name="toolChoice"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex gap-2">
                    <ToolChoiceDropdownMenu
                      showCustom={showCustomToolChoice}
                      value={field.value}
                      setValue={field.onChange}
                      align="start"
                      isReadOnly={isReadOnly}
                      className="disabled:opacity-100"
                    />
                    {showCustomToolChoice && (
                      <Input
                        className="h-7 flex-1 text-xs disabled:cursor-default disabled:opacity-100"
                        placeholder="Enter function name"
                        value={watchedToolChoice.function.name}
                        disabled={isReadOnly}
                        onChange={(e) => {
                          field.onChange(
                            {
                              type: "function",
                              function: { name: e.target.value },
                            },
                            {
                              shouldDirty: true,
                              shouldTouch: true,
                            },
                          );
                        }}
                      />
                    )}
                  </div>
                </FormControl>
                {rawToolsErrors && (
                  <div className="text-xs font-medium text-bad-700">
                    {rawToolsErrors}
                  </div>
                )}

                <FormDescription>{toolsChoiceDescription}</FormDescription>
              </FormItem>
            )}
          />
        </div>
        {!isReadOnly && (
          <DialogFooter className="flex-none border-t p-4">
            <Button
              type="submit"
              variant="success"
              size="xs"
              isLoading={form.formState.isSubmitting}
              disabled={!form.formState.isValid || !form.formState.isDirty}
            >
              Save tools
            </Button>
          </DialogFooter>
        )}
      </form>
    </Form>
  );
};
