use std::collections::HashMap;
use util::{
    chrono::{Duration, Utc},
    system_types::FullObjectId,
    test_util::assert_hashmap_eq,
    uuid::Uuid,
    xact::TransactionId,
};

use crate::{
    config_with_store::ConfigWithStore,
    index_wal_reader::{IndexWalReader, IndexWalReaderInput},
    index_wal_reader_test_util::get_reader_full_docs,
    test_util::collect_wal_stream,
    vacuum::CommonVacuumOptions,
    vacuum::VacuumType,
    vacuum_index::{
        vacuum_index, vacuum_index_stateless, VacuumIndexFullOptions, VacuumIndexInput,
        VacuumIndexOptionalInput, VacuumIndexOptions, VacuumIndexOutput,
    },
    vacuum_segment_wal::{
        vacuum_segment_wal, VacuumSegmentWalFullOptions, VacuumSegmentWalInput,
        VacuumSegmentWalOptionalInput, VacuumSegmentWalOptions, VacuumSegmentWalOutput,
    },
    wal::{wal_stream, WALScope, Wal},
    wal_entry::Wal<PERSON>ntry,
};

pub fn default_common_vacuum_opts_for_testing() -> CommonVacuumOptions {
    CommonVacuumOptions {
        vacuum_deletion_grace_period_seconds: 0,
        vacuum_last_written_slop_seconds: 0,
        ..Default::default()
    }
}

pub fn default_vacuum_index_full_opts_for_testing() -> VacuumIndexFullOptions {
    VacuumIndexFullOptions {
        common_vacuum_opts: default_common_vacuum_opts_for_testing(),
        vacuum_index_opts: VacuumIndexOptions {
            vacuum_index_period_seconds: 2 * 24 * 60 * 60, // 2 days
            vacuum_index_delete_unrecognized_files: true,
            ..Default::default()
        },
    }
}

pub fn default_vacuum_segment_wal_full_opts_for_testing() -> VacuumSegmentWalFullOptions {
    VacuumSegmentWalFullOptions {
        common_vacuum_opts: default_common_vacuum_opts_for_testing(),
        vacuum_segment_wal_opts: VacuumSegmentWalOptions {
            vacuum_segment_wal_entry_expiration_seconds: 0,
            vacuum_segment_wal_period_seconds: 2 * 24 * 60 * 60, // 2 days
            vacuum_segment_wal_delete_unrecognized_files: true,
            ..Default::default()
        },
    }
}

pub struct VacuumThenValidateIndexWalArgs<'a> {
    pub config_with_store: &'a ConfigWithStore,
    pub full_schema: util::schema::Schema,
    pub object_ids: Option<&'a [FullObjectId<'a>]>,
    pub stateless: bool,
    pub options: VacuumIndexFullOptions,
    pub expected_segment_id_cursor: Option<Option<Uuid>>,
    pub dry_run: bool,
}

pub async fn force_vacuum_then_validate_index_wal<'a>(
    args: VacuumThenValidateIndexWalArgs<'a>,
) -> VacuumIndexOutput {
    let global_store = args.config_with_store.global_store.clone();

    let object_ids_owned = if let Some(ids) = args.object_ids {
        ids.iter().map(|id| id.to_owned()).collect()
    } else {
        global_store
            .list_object_ids(None, i32::MAX as usize, None)
            .await
            .unwrap()
    };
    let object_ids = object_ids_owned
        .iter()
        .map(|id| id.as_ref())
        .collect::<Vec<_>>();

    let segment_ids = global_store
        .list_segment_ids(object_ids.as_slice(), None)
        .await
        .unwrap()
        .into_iter()
        .flatten()
        .collect::<Vec<_>>();

    // Set last_successful_start_ts to:
    //
    //   min(now() - vacuum_period, last_written_ts + deletion_grace_period + last_written_slop_seconds - 1 second)
    //
    // so that vacuum actually picks up the segments.
    let segment_id_to_vacuum_state = global_store
        .query_segment_vacuum_state(&segment_ids)
        .await
        .unwrap();
    for segment_id in &segment_ids {
        let last_written_ts = segment_id_to_vacuum_state[segment_id].last_written_ts;
        let last_successful_start_ts = (last_written_ts
            + Duration::seconds(
                args.options
                    .common_vacuum_opts
                    .vacuum_deletion_grace_period_seconds
                    + args
                        .options
                        .common_vacuum_opts
                        .vacuum_last_written_slop_seconds
                    - 1,
            ))
        .min(
            Utc::now()
                - Duration::seconds(args.options.vacuum_index_opts.vacuum_index_period_seconds),
        );
        global_store
            .upsert_segment_vacuum_last_successful_start_ts(
                &[*segment_id],
                VacuumType::VacuumIndex,
                last_successful_start_ts,
            )
            .await
            .unwrap();
    }

    let output = vacuum_then_validate_index_wal(args).await;

    // Assert that vacuum actually ran on all of the included segments.
    let expected_num_processed_segments = segment_ids.len();
    assert_eq!(
        output.num_processed_segments,
        expected_num_processed_segments
    );

    output
}

pub async fn vacuum_then_validate_index_wal<'a>(
    args: VacuumThenValidateIndexWalArgs<'a>,
) -> VacuumIndexOutput {
    let global_store = args.config_with_store.global_store.clone();

    let object_ids_owned = if let Some(ids) = args.object_ids {
        ids.iter().map(|id| id.to_owned()).collect()
    } else {
        global_store
            .list_object_ids(None, i32::MAX as usize, None)
            .await
            .unwrap()
    };
    let object_ids = object_ids_owned
        .iter()
        .map(|id| id.as_ref())
        .collect::<Vec<_>>();

    let mut readers = HashMap::new();
    for object_id in &object_ids {
        let reader = IndexWalReader::new(
            IndexWalReaderInput {
                config_with_store: args.config_with_store,
                full_schema: args.full_schema.clone(),
                object_ids: &[object_id.to_owned()],
                filters: &[],
                sort: &None,
                partition_all: false,
            },
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap();

        readers.insert(object_id, reader);
    }

    let mut object_id_to_index_wal_entries = HashMap::new();
    for object_id in &object_ids {
        object_id_to_index_wal_entries.insert(
            object_id,
            get_reader_full_docs(&readers[object_id])
                .await
                .into_iter()
                .map(|(key, doc)| (key, doc.to_sanitized().unwrap()))
                .collect(),
        );
    }

    let input = VacuumIndexInput {
        // Use `args.object_ids` here instead of `object_ids` so we test feeding `object_ids = None`
        // to vacuum all objects.
        object_ids: args.object_ids,
        global_store: args.config_with_store.global_store.clone(),
        index_store: args.config_with_store.index.clone(),
        locks_manager: &*args.config_with_store.locks_manager,
        config_file_schema: Some(args.full_schema),
        dry_run: args.dry_run,
        segment_ids: None,
    };
    let optional_input = VacuumIndexOptionalInput::default();

    let output = if args.stateless {
        let output = vacuum_index_stateless(input, optional_input, args.options).await;
        assert!(output.success, "Expected success, got: {:?}", output.error);
        assert!(
            output.error.is_none(),
            "Expected no error, got: {:?}",
            output.error
        );
        if let Some(expected_segment_id_cursor) = args.expected_segment_id_cursor {
            assert_eq!(output.segment_id_cursor, expected_segment_id_cursor);
        }

        VacuumIndexOutput {
            num_processed_segments: output.num_processed_segments,
            planned_num_deletes: output.planned_num_deletes,
            planned_total_bytes: output.planned_total_bytes,
            num_deleted_files: output.num_deleted_files,
        }
    } else {
        vacuum_index(input, optional_input, &args.options)
            .await
            .unwrap()
    };

    // Assert that the post-vacuum index WAL entries are the same as the pre-vacuum entries.
    for object_id in &object_ids {
        let after_index_wal_entries = get_reader_full_docs(&readers[object_id])
            .await
            .into_iter()
            .map(|(key, doc)| (key, doc.to_sanitized().unwrap()))
            .collect();
        assert_hashmap_eq(
            &after_index_wal_entries,
            &object_id_to_index_wal_entries[object_id],
        );
    }

    output
}

fn flatten_wal_entries(entries: Vec<(TransactionId, Vec<WalEntry>)>) -> Vec<WalEntry> {
    entries
        .into_iter()
        .flat_map(|(_, entries)| entries)
        .collect()
}

pub struct VacuumSegmentWalForTestingInput<'a> {
    pub config_with_store: &'a ConfigWithStore,
    pub object_ids: Option<&'a [FullObjectId<'a>]>,
}

pub struct VacuumSegmentWalForTestingOptionalInput {
    pub force_vacuum: bool,
}

impl Default for VacuumSegmentWalForTestingOptionalInput {
    fn default() -> Self {
        Self { force_vacuum: true }
    }
}

pub async fn vacuum_segment_wal_for_testing<'a>(
    input: VacuumSegmentWalForTestingInput<'a>,
    optional_input: VacuumSegmentWalForTestingOptionalInput,
    options: VacuumSegmentWalFullOptions,
) -> VacuumSegmentWalOutput {
    let global_store = input.config_with_store.global_store.clone();

    let object_ids_owned = if let Some(ids) = input.object_ids {
        ids.iter().map(|id| id.to_owned()).collect()
    } else {
        global_store
            .list_object_ids(None, i32::MAX as usize, None)
            .await
            .unwrap()
    };
    let object_ids = object_ids_owned
        .iter()
        .map(|id| id.as_ref())
        .collect::<Vec<_>>();
    let segment_ids = global_store
        .list_segment_ids(object_ids.as_slice(), None)
        .await
        .unwrap()
        .into_iter()
        .flatten()
        .collect::<Vec<_>>();

    let wal = input.config_with_store.segment_index_wal();

    let mut segment_id_to_before_vacuum_wal_entries = HashMap::new();
    for segment_id in &segment_ids {
        segment_id_to_before_vacuum_wal_entries.insert(
            segment_id,
            flatten_wal_entries(
                collect_wal_stream(wal_stream(
                    wal.wal_metadata_stream(WALScope::Segment(*segment_id), Default::default())
                        .await
                        .unwrap(),
                    Default::default(),
                ))
                .await
                .unwrap(),
            ),
        );
    }

    let execute_vacuum_segment_wal = || async {
        vacuum_segment_wal(
            VacuumSegmentWalInput {
                object_ids: input.object_ids,
                global_store: input.config_with_store.global_store.clone(),
                index_store: input.config_with_store.index.clone(),
                locks_manager: &*input.config_with_store.locks_manager,
                dry_run: false,
            },
            VacuumSegmentWalOptionalInput::default(),
            &options,
        )
        .await
        .unwrap()
    };

    let output = if optional_input.force_vacuum {
        // Set last_successful_start_ts to:
        //
        //   min(
        //     now() - vacuum_period,
        //     last_written_ts
        //       + max(deletion_grace_period, entry_expiration_seconds)
        //       + last_written_slop_seconds
        //       - 1 second
        //   )
        //
        // to ensure beyond any doubt that vacuum will pick up the segment.
        let segment_id_to_vacuum_state = global_store
            .query_segment_vacuum_state(&segment_ids)
            .await
            .unwrap();
        for segment_id in &segment_ids {
            let last_written_ts = segment_id_to_vacuum_state[segment_id].last_written_ts;
            let last_successful_start_ts = (last_written_ts
                + Duration::seconds(
                    std::cmp::max(
                        options
                            .common_vacuum_opts
                            .vacuum_deletion_grace_period_seconds,
                        options
                            .vacuum_segment_wal_opts
                            .vacuum_segment_wal_entry_expiration_seconds,
                    ) + options.common_vacuum_opts.vacuum_last_written_slop_seconds
                        - 1,
                ))
            .min(
                Utc::now()
                    - Duration::seconds(
                        options
                            .vacuum_segment_wal_opts
                            .vacuum_segment_wal_period_seconds,
                    ),
            );
            global_store
                .upsert_segment_vacuum_last_successful_start_ts(
                    &[*segment_id],
                    VacuumType::VacuumSegmentWal,
                    last_successful_start_ts,
                )
                .await
                .unwrap();
        }

        let output = execute_vacuum_segment_wal().await;

        // Assert that vacuum actually ran on all of the included segments.
        let expected_num_processed_segments = segment_ids.len();
        assert_eq!(
            output.num_processed_segments,
            expected_num_processed_segments
        );

        output
    } else {
        execute_vacuum_segment_wal().await
    };

    let mut segment_id_to_after_vacuum_wal_entries = HashMap::new();
    for segment_id in &segment_ids {
        segment_id_to_after_vacuum_wal_entries.insert(
            segment_id,
            flatten_wal_entries(
                collect_wal_stream(wal_stream(
                    wal.wal_metadata_stream(WALScope::Segment(*segment_id), Default::default())
                        .await
                        .unwrap(),
                    Default::default(),
                ))
                .await
                .unwrap(),
            ),
        );
    }

    // Validate that the wal entries before and after vacuum are the same.
    for segment_id in &segment_ids {
        assert_eq!(
            segment_id_to_before_vacuum_wal_entries[segment_id],
            segment_id_to_after_vacuum_wal_entries[segment_id]
        );
    }

    output
}
