---
title: "OpenAI Agents SDK"
---

import { CodeTabs, PYTab, TSTab } from "#/ui/docs/code-tabs";
import ModuleInstaller from "#/ui/docs/module-installer";

# OpenAI Agents SDK

The Braintrust SDK provides trace processors for the OpenAI Agents SDK
that send traces and spans to Braintrust for monitoring and evaluation.

<CodeTabs>

<PYTab>

When installed with the `openai-agents` extra,
the Braintrust SDK provides a `tracing.TracingProcessor` implementation
that sends the traces and spans from the OpenAI Agents SDK to Braintrust.

<ModuleInstaller languages={["py"]} packageNames="braintrust[openai-agents]" />

</PYTab>

<TSTab>

For TypeScript, install the Braintrust OpenAI Agents integration package:

<ModuleInstaller languages={["ts"]} packageNames="braintrust @braintrust/openai-agents @openai/agents" />

</TSTab>

</CodeTabs>

<CodeTabs>

<PYTab>

```py title="trace-agents.py"
import asyncio

from agents import Agent, <PERSON>, set_trace_processors
from braintrust import init_logger
from braintrust.wrappers.openai import BraintrustTracingProcessor


async def main():
    agent = Agent(
        name="Assistant",
        instructions="You only respond in haikus.",
    )

    result = await Runner.run(agent, "Tell me about recursion in programming.")
    print(result.final_output)


if __name__ == "__main__":
    set_trace_processors([BraintrustTracingProcessor(init_logger("openai-agent"))])
    asyncio.run(main())
```

</PYTab>

<TSTab>

```typescript title="trace-agents.ts" #skip-compile
import { initLogger } from "braintrust";
import { OpenAIAgentsTraceProcessor } from "@braintrust/openai-agents";
import { Agent, run, addTraceProcessor } from "@openai/agents";

// Initialize Braintrust logger
const logger = initLogger({
  projectName: "openai-agent",
});

// Create the tracing processor
const processor = new OpenAIAgentsTraceProcessor({ logger });

// Add the processor to OpenAI Agents
addTraceProcessor(processor);

async function main() {
  const agent = new Agent({
    name: "Assistant",
    model: "gpt-4o-mini",
    instructions: "You only respond in haikus.",
  });

  const result = await run(agent, "Tell me about recursion in programming.");
  console.log(result.finalOutput);
}

main().catch(console.error);
```

</TSTab>

</CodeTabs>

The constructor of the tracing processor can take a `braintrust.Span`, `braintrust.Experiment`, or `braintrust.Logger`
that serves as the root under which all spans will be logged.
If not provided, the current span, experiment, or logger
is selected automatically.

![OpenAI Agents SDK Logs](/docs/oai-agents-sdk-logs.png)

The Agents SDK can also be used to implement a `task` in an `Eval`,
making it straightforward to build and evaluate agentic workflows:

<CodeTabs>

<PYTab>

```py title="eval-agents.py"
from agents import Agent, Runner, set_trace_processors
from autoevals import ClosedQA
from braintrust import Eval
from braintrust.wrappers.openai import BraintrustTracingProcessor

set_trace_processors([BraintrustTracingProcessor()])


async def task(input: str):
    agent = Agent(
        name="Assistant",
        instructions="You only respond in haikus.",
    )

    result = await Runner.run(agent, input)
    return result.final_output


Eval(
    name="openai-agent",
    data=[
        {
            "input": "Tell me about recursion in programming.",
        }
    ],
    task=task,
    scores=[
        ClosedQA.partial(
            criteria="The response should respond to the prompt and be a haiku.",
        )
    ],
)
```

</PYTab>

<TSTab>

```typescript title="eval-agents.ts" #skip-compile
import { Agent, run, addTraceProcessor } from "@openai/agents";
import { OpenAIAgentsTraceProcessor } from "@braintrust/openai-agents";
import { Eval } from "braintrust";

// Set up the trace processor
const processor = new OpenAIAgentsTraceProcessor();
addTraceProcessor(processor);

async function task(input: string) {
  const agent = new Agent({
    name: "Assistant",
    model: "gpt-4o-mini",
    instructions: "You only respond in haikus.",
  });

  const result = await run(agent, input);
  return result.finalOutput;
}

Eval("openai-agent", {
  data: [
    {
      input: "Tell me about recursion in programming.",
    },
  ],
  task,
  scores: [
    // You can use autoevals or custom scoring functions
    {
      name: "haiku_check",
      scorer: async ({ output }) => {
        // Custom scoring logic for haiku validation
        const lines = output.split("\n").filter((line) => line.trim());
        return lines.length === 3 ? 1 : 0;
      },
    },
  ],
});
```

</TSTab>

</CodeTabs>

![OpenAI Agents SDK Eval](/docs/oai-agents-sdk-eval.png)
