import { type Dispatch, type SetStateAction, useMemo } from "react";
import { type InferField } from "./filter-editor-infer-data";
import { Combobox } from "#/ui/combobox/combobox";
import { Input } from "#/ui/input";

export const FilterEditorTopValuesSelector = (props: {
  inferField?: InferField;
  value: string;
  setValue: Dispatch<SetStateAction<string>>;
  onEnter?: () => void;
  showCounts: boolean;
  isScore: boolean;
  hasCustomField: boolean;
  hasCustomValue: boolean;
  setHasSelectedCustomValue: Dispatch<SetStateAction<boolean>>;
}) => {
  const {
    inferField,
    value,
    setValue,
    onEnter,
    showCounts,
    isScore,
    hasCustomField,
    hasCustomValue,
    setHasSelectedCustomValue,
  } = props;

  const selectedTopValues = useMemo(() => {
    const rawTopValues = inferField?.topValues ?? [];

    // trim and filter values
    const filteredTopValues = rawTopValues
      .map((t) => ({ ...t, value: String(t.value).trim() }))
      .filter((t) => Boolean(t.value));

    // dedupe and add count
    const valueToCount = new Map<string, number>();
    filteredTopValues.forEach((t) => {
      valueToCount.set(t.value, (valueToCount.get(t.value) ?? 0) + t.count);
    });

    // remap and sort by
    const dedupedAndSorted = [...valueToCount.entries()]
      .map(([value, count]) => ({ value, count }))
      .toSorted((a, b) => {
        if (isScore) {
          const aVal = parseFloat(a.value);
          const bVal = parseFloat(b.value);
          if (Number.isFinite(aVal) && Number.isFinite(bVal)) {
            return bVal - aVal;
          }
          if (Number.isFinite(bVal)) {
            return 1;
          }
          if (Number.isFinite(aVal)) {
            return -1;
          }
        }
        return b.count - a.count;
      });
    return dedupedAndSorted;
  }, [inferField, isScore]);

  const options = useMemo(() => {
    const options = selectedTopValues.map(
      (t): { label: string; value: string; count: number | null } => {
        const name = t.value;
        if (isScore) {
          const score = parseFloat(name);
          if (Number.isFinite(score)) {
            const scoreValue = Math.max(0, Math.min(100, 100 * score));
            const scoreFormatted = `${scoreValue.toFixed(2)}%`;
            const valueStr = String(scoreValue);
            return {
              label: scoreFormatted,
              value: valueStr,
              count: t.count,
            };
          }
        }
        return {
          label: name,
          value: name,
          count: t.count,
        };
      },
    );
    if (!options.some((o) => o.value === value) && value && !hasCustomValue) {
      options.push({
        label: value,
        value: value,
        count: null,
      });
    }

    return options;
  }, [value, selectedTopValues, isScore, hasCustomValue]);

  return (
    <>
      {options.length > 0 && !hasCustomField && (
        <Combobox
          options={options}
          noSearch={options.length < 5}
          variant="button"
          buttonSize="xs"
          searchPlaceholder="Search existing values"
          selectedValue={hasCustomValue ? "" : value}
          placeholderLabel="Custom value"
          itemLabelClassName="line-clamp-3"
          align="start"
          onChange={(v) => {
            setHasSelectedCustomValue(false);
            setValue(v ?? "");
          }}
          renderOptionLabel={(opt) => {
            if (!showCounts || opt.count == null) {
              return opt.label;
            }
            return (
              <span className="flex w-full justify-between gap-1">
                <span>{opt.label}</span>
                <span className="right text-[10px] tabular-nums text-primary-500">
                  {opt.count}
                </span>
              </span>
            );
          }}
          bottomActions={[
            {
              label: "Use a custom value",
              onSelect: () => {
                setHasSelectedCustomValue(true);
                setValue("");
              },
              selected: Boolean(hasCustomValue),
            },
          ]}
        />
      )}
      {hasCustomValue && (
        <div className="flex">
          <Input
            autoFocus
            type="text"
            placeholder="Enter value"
            name="value"
            value={value}
            onChange={(e) => {
              setValue(e.target.value);
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                onEnter?.();
              }
            }}
            className="ml-2 h-7 px-2 text-xs"
          />
        </div>
      )}
    </>
  );
};
