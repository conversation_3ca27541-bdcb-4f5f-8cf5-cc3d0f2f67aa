import { HTTPError } from "#/utils/server-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { SqlQueryParams } from "#/utils/sql-query-params";
import { isEmpty, isObject } from "#/utils/object";
import {
  type AclObjectType,
  type Permission,
  appLimitParamSchema,
  startingAfterSchema,
  endingBeforeSchema,
} from "@braintrust/typespecs";
import { type BtPgClient } from "@braintrust/local/bt-pg";
import { doubleQuote } from "#/utils/sql-utils";
import {
  type AclObjectTableInfo,
  type OptionalLinkageSpec,
  type TableInfoObjectType,
  aclObjectTableInfos,
  aclSpecs,
  isOrgLinkageOptional,
  isProjectLinkageOptional,
} from "@braintrust/local/app-schema";
import { type AuthLookup } from "./_lookup_api_key";
import {
  type FormatNotFoundErrorMessageInput,
  formatNotFoundErrorMessage,
} from "@braintrust/local";
import { z } from "zod";
import { objectNullish } from "braintrust/util";
import { ANON_USER_ID } from "#/utils/constants";
//import { substituteParamsDebug } from "#/utils/sql-query-params";

export function resolveNonVirtualTableInfo(
  objectType: TableInfoObjectType,
): AclObjectTableInfo {
  const { kind, data } = aclObjectTableInfos[objectType];
  if (kind === "virtual") {
    throw new Error(`Expected full table entry for object type ${objectType}`);
  }
  return data;
}

export function resolvePossiblyVirtualTableInfo(
  objectType: TableInfoObjectType,
): AclObjectTableInfo {
  const { kind, data } = aclObjectTableInfos[objectType];
  return kind === "virtual" ? resolveNonVirtualTableInfo(data) : data;
}

function makeTableIdentifier({
  tableSchema,
  tableName,
}: {
  tableSchema?: string;
  tableName: string;
}) {
  return tableSchema
    ? `${doubleQuote(tableSchema)}.${doubleQuote(tableName)}`
    : doubleQuote(tableName);
}

export function fromTablesClause(tableInfoObjectTypes: TableInfoObjectType[]) {
  const nonVirtualTableInfos = tableInfoObjectTypes.reduce((acc, obj) => {
    const tableInfo = aclObjectTableInfos[obj];
    if (tableInfo.kind === "table") {
      return acc.concat([{ objectType: obj, tableInfo: tableInfo.data }]);
    } else {
      return acc;
    }
  }, new Array<{ objectType: TableInfoObjectType; tableInfo: AclObjectTableInfo }>());
  if (nonVirtualTableInfos.length === 0) {
    throw new Error(
      `Object hierarchy ${JSON.stringify(
        tableInfoObjectTypes,
      )} does not point to any real tables`,
    );
  }
  return nonVirtualTableInfos
    .map((objectInfo, idx) => {
      const { objectType, tableInfo } = objectInfo;
      if (idx === 0) {
        return makeTableIdentifier(tableInfo);
      }
      const matchingPrevTables = nonVirtualTableInfos.slice(0, idx).reduce(
        (
          acc: {
            tableSchema: string | undefined;
            tableName: string;
            joinCol: string;
          }[],
          prevTableInfo,
        ) => {
          const foreignKeyCol =
            prevTableInfo.tableInfo.foreignKeyIds?.[objectType];
          if (foreignKeyCol) {
            return [
              ...acc,
              {
                tableSchema: prevTableInfo.tableInfo.tableSchema,
                tableName: prevTableInfo.tableInfo.tableName,
                joinCol: foreignKeyCol,
              },
            ];
          } else {
            return acc;
          }
        },
        [],
      );
      if (!matchingPrevTables.length) {
        throw new Error(
          `No join col defined for joining to ${tableInfo.tableName}`,
        );
      } else if (matchingPrevTables.length > 1) {
        throw new Error(
          `Multiple join cols defined for joining to ${tableInfo.tableName}`,
        );
      }
      const prevTableInfo = matchingPrevTables[0];
      return `left join ${makeTableIdentifier(tableInfo)} on ${makeTableIdentifier(prevTableInfo)}.${doubleQuote(prevTableInfo.joinCol)} = ${makeTableIdentifier(tableInfo)}.id`;
    })
    .join("\n\t");
}

export function isActiveChecks(objectTypes: TableInfoObjectType[]) {
  return ["true"]
    .concat(
      objectTypes.map((objectType) => {
        const { kind, data: tableInfo } = aclObjectTableInfos[objectType];
        if (kind === "virtual" || !tableInfo.deletedAtCol) {
          return "true";
        } else {
          return `${makeTableIdentifier(tableInfo)}.${doubleQuote(
            tableInfo.deletedAtCol,
          )} isnull`;
        }
      }),
    )
    .join(" and ");
}

export function aclCheckConditions(args: {
  queryParams: SqlQueryParams;
  user_id: string;
  // Adding the ACL checks for anynomus access comes at a performance cost. In
  // general we should only enable it if the query is for a single (or small
  // finite set) of objects.
  allowAnonAccess: boolean;
  permission?: Permission;
  baseObjectType: AclObjectType;
  parentObjectTypes: AclObjectType[];
  overrideRestrictObjectType?: AclObjectType | "null";
  overrideAclTableName?: string;
}) {
  const userIdParam = args.queryParams.add(args.user_id);
  const permissionParam = args.permission
    ? args.queryParams.add(args.permission)
    : undefined;
  const restrictObjectTypeParam =
    args.overrideRestrictObjectType === "null"
      ? null
      : args.queryParams.add(
          args.overrideRestrictObjectType ?? args.baseObjectType,
        );

  const aclTableName = makeTableIdentifier({
    tableName: args.overrideAclTableName ?? "_expanded_acls",
  });
  let userGroupIdFilter = `${aclTableName}.user_group_id = ${userIdParam}`;
  // Whitelisted leaf object types to allow non-org, non-anon access.
  if (
    args.allowAnonAccess &&
    ["dataset", "experiment", "prompt", "project_log"].includes(
      args.baseObjectType,
    )
  ) {
    userGroupIdFilter += ` or ${aclTableName}.user_group_id = '${ANON_USER_ID}'`;
  }

  const restrictObjectTypeFilter =
    restrictObjectTypeParam == null
      ? "restrict_object_type is null"
      : `(restrict_object_type is null or restrict_object_type = ${restrictObjectTypeParam})`;

  return `
    (${[args.baseObjectType]
      .concat(args.parentObjectTypes)
      .map(
        (objectType) => `
        (
            ${aclTableName}.object_type = ${args.queryParams.add(objectType)}
            and ${aclTableName}.object_id = ${makeTableIdentifier(resolvePossiblyVirtualTableInfo(objectType))}.id
            and ${aclTableName}.user_object_type = 'user'
            and (${userGroupIdFilter})
            ${permissionParam ? `and ${aclTableName}.permission = ${permissionParam}` : ""}
            and ${restrictObjectTypeFilter}
        )`,
      )
      .join(" or ")})`;
}

export type QueryFilterParamBase = string | number | boolean;

export class CustomQueryFilter<T> {
  constructor(
    public data: {
      values: [T, ...T[]];
      tableSchema?: string;
      tableName: string;
      col: string;
      allowNull?: boolean;
    },
  ) {}
}

export class JsonbPathEqualityFilter {
  // The input filters are a nested dictionary of keys and values. We translate them to a set of JSONB path equality filters.
  //
  // For example, given an input of `{ foo: { bar: "baz" }, green: null }`, we
  // translate that to the following values: `['$.foo.bar == "baz"', '$.green ==
  // null']`.
  constructor(public filters: Record<string, unknown>) {}

  public getValues(): string[] {
    const values: string[] = [];

    const processPath = (obj: unknown, currentPath: string) => {
      if (isObject(obj)) {
        for (const [key, value] of Object.entries(obj)) {
          const newPath = currentPath ? `${currentPath}.${key}` : `$.${key}`;
          processPath(value, newPath);
        }
      } else if (Array.isArray(obj)) {
        throw new HTTPError(
          400,
          "Path equality filters do not support array values",
        );
      } else {
        values.push(`${currentPath} == ${JSON.stringify(obj)}`);
      }
    };

    processPath(this.filters, "");

    return values;
  }
}

export type QueryFilterParamValue<T extends QueryFilterParamBase> =
  | T
  | [T, ...T[]]
  | CustomQueryFilter<T>
  | JsonbPathEqualityFilter;

function queryFilterValueTyped<T extends QueryFilterParamBase>(
  param: QueryFilterParamValue<T>,
): [T, ...T[]] {
  if (param instanceof JsonbPathEqualityFilter) {
    throw new Error(
      "Cannot call queryFilterValueTyped on a JsonbPathEqualityFilter",
    );
  }
  return param instanceof CustomQueryFilter
    ? param.data.values
    : Array.isArray(param)
      ? param
      : [param];
}

function queryFilterValueUntyped(
  param: QueryFilterParamValue<QueryFilterParamBase>,
): unknown[] {
  return param instanceof CustomQueryFilter
    ? param.data.values
    : param instanceof JsonbPathEqualityFilter
      ? param.getValues()
      : Array.isArray(param)
        ? param
        : [param];
}

export type QueryFilterParams = {
  // Specially-typed filters.
  name?: QueryFilterParamValue<string> | null;
  id?: QueryFilterParamValue<string> | null;
  // Any remaining filters.
  [key: string]: QueryFilterParamValue<QueryFilterParamBase> | null | undefined;
};

export type QueryFilterOpts = {
  // Groups of filters that should be OR'd together. Expects all specified
  // filters are provided in the associated `QueryFilterParams`.
  //
  // Any filters not mentioned will be placed in their own group (i.e. AND'ed
  // together).
  orSets?: string[][];
};

export function genQueryFilters(
  defaultTableInfo: {
    tableSchema?: string;
    tableName: string;
    optionalLinkageSpec?: OptionalLinkageSpec;
  },
  queryParams: SqlQueryParams,
  filters: QueryFilterParams,
  authLookup?: AuthLookup,
  opts?: QueryFilterOpts,
) {
  const projectLinkageOptional = isProjectLinkageOptional(
    defaultTableInfo.optionalLinkageSpec,
  );
  const orgLinkageOptional = isOrgLinkageOptional(
    defaultTableInfo.optionalLinkageSpec,
  );

  function defaultParams(key: string): {
    tableSchema: string | undefined;
    tableName: string;
    col: string;
    allowNull: boolean;
  } {
    const [schema, table, col, allowNull] = (() => {
      const ALLOW_NULL = true;
      switch (key) {
        case "project_id":
          return [undefined, "projects", "id", projectLinkageOptional];
        case "project_name":
          return [undefined, "projects", "name", projectLinkageOptional];
        case "org_id":
          return [undefined, "organizations", "id", orgLinkageOptional];
        case "org_name":
          return [undefined, "organizations", "name", orgLinkageOptional];
        default:
          return [
            defaultTableInfo.tableSchema,
            defaultTableInfo.tableName,
            key,
            !ALLOW_NULL,
          ];
      }
    })();
    return { tableSchema: schema, tableName: table, col, allowNull };
  }

  function singleFilter(key: string, mapped: QueryFilterParams[string]) {
    if (isEmpty(mapped)) {
      return "true";
    }
    const values = queryFilterValueUntyped(mapped);
    const { tableSchema, tableName, col, allowNull } =
      mapped instanceof CustomQueryFilter ? mapped.data : defaultParams(key);
    const tableInfo = { tableSchema, tableName };
    const tableCol = `${makeTableIdentifier(tableInfo)}.${doubleQuote(col)}`;
    const allowNullExpr = allowNull ? `${tableCol} isnull or ` : "";
    if (values.length === 0) {
      return "false";
    }
    if (mapped instanceof JsonbPathEqualityFilter) {
      return (
        "(" +
        values
          .map((v) => `(${allowNullExpr}${tableCol} @@ ${queryParams.add(v)})`)
          .join(" and ") +
        ")"
      );
    } else {
      if (values.length === 1) {
        return `(${allowNullExpr}${tableCol} = ${queryParams.add(values[0])})`;
      } else {
        return `(${allowNullExpr}${tableCol} in (${values.map((v) => queryParams.add(v)).join(", ")}))`;
      }
    }
  }

  const keyToFilterExpr = Object.fromEntries(
    Object.entries(filters).map(([key, value]) => [
      key,
      singleFilter(key, value),
    ]),
  );
  opts?.orSets?.forEach((orSet) => {
    if (!orSet.length) {
      throw new Error("Cannot specify empty orSet");
    }
  });
  const orSetKeys = new Set<string>(opts?.orSets?.flat() ?? []);
  const orSets: string[][] = (opts?.orSets ?? []).concat(
    Object.keys(filters)
      .filter((key) => !orSetKeys.has(key))
      .map((key) => [key]),
  );
  // When making queries with an org-scoped auth token, restrict the set of
  // objects to those that are associated with the scoped org. Note that
  // permission to access the object is checked separately: this is just a
  // heuristic to restrict which objects are returned for users who are part of
  // multiple organizations.
  let authLookupCond = "true";
  if (authLookup?.org_id) {
    // If the user is authenticated to an org, they should be able to see
    authLookupCond = [
      orgLinkageOptional ? "organizations.id is null" : "false", // objects not associated with an org
      `organizations.id = ${queryParams.add(authLookup.org_id)}`, // objects associated with the authenticated org
      `(not exists(select 1 from members where org_id = organizations.id and user_id = ${queryParams.add(authLookup.user_id)}))`, // objects in orgs that the user is not a member of (for which the user has an ACL, which is checked separately)
    ].join(" or ");
    authLookupCond = `(${authLookupCond})`;
  }

  return orSets
    .map((orSet) => orSet.map((key) => keyToFilterExpr[key]).join(" or "))
    .concat([authLookupCond])
    .join(" and ");
}

// Checks if the given filters and options specify a filter expression for just
// the given key.
function hasSingleFilter(
  filters: QueryFilterParams,
  key: string,
  opts?: QueryFilterOpts,
) {
  return (
    filters[key] &&
    (!opts?.orSets ||
      opts.orSets.some((orSet) => orSet.length === 1 && orSet[0] === key))
  );
}

type AclPermissionInfo = {
  aclObjectType: AclObjectType;
  aclPermission: Permission;
  overrideRestrictObjectType?: AclObjectType | "null";
  aclCheckAdditionalRowsQuery?: string;
};

type PartialAclPermissionInfo = Omit<AclPermissionInfo, "aclPermission"> &
  Partial<Pick<AclPermissionInfo, "aclPermission">>;

type PermissionInfo = AclPermissionInfo | "org-membership";

function isAclPermissionInfo(x: PermissionInfo): x is AclPermissionInfo {
  return x instanceof Object && "aclObjectType" in x;
}

function isPartialAclPermissionInfo(
  x: PartialPermissionInfo,
): x is PartialAclPermissionInfo {
  return x instanceof Object && "aclObjectType" in x;
}

type PartialPermissionInfo =
  | Exclude<PermissionInfo, AclPermissionInfo>
  | PartialAclPermissionInfo;

type FullResultSetQueryParams = {
  authLookup: AuthLookup;
  permissionInfo: PermissionInfo;
  startingParams?: SqlQueryParams;
  // Tables to join in before joining in the ACL tables. Can be useful for
  // querying tables which are not directly tied to the ACL hierarchy.
  priorObjectTables?: TableInfoObjectType[];
  // A subset of the columns in the base table to return. It's important that
  // this is a set so a malicious caller cannot add the same column multiple
  // times.
  fullResultSetSubsetProjection?: Set<string>;
  // Additional projections in addition to the full set of columns from the
  // first queried table. These can reference any of the joined-in tables.
  fullResultSetAdditionalProjections?: string[];
  // If true, skip the "deleted_at isnull" checks on all joined tables.
  consider_deleted?: boolean | null;
  // A set of { key: value } pairs to filter the results by.
  filters?: QueryFilterParams;
  filterOpts?: QueryFilterOpts;
  // Sysadmins are granted special permissions to look up project automations.
  // We may want to unwind this later.
  isSysadminAutomationLookup?: boolean;
};

// Some CRUD operations allow providing a default ACL permission, so we accept a
// query input that allows omitting the ACL permission.
type FullResultSetQueryParamsPartial = Omit<
  FullResultSetQueryParams,
  "permissionInfo"
> & { permissionInfo: PartialPermissionInfo };

type FullResultSetQueryOverrideParams = {
  fullResultsQueryOverride: string;
  baseTableOverride?: TableInfoObjectType;
  startingParams?: SqlQueryParams;
};

// Most CRUD queries have an expected results size. The user can set a
// `fullResultsSize` to ensure the query reads/writes exactly that number of
// results, or does nothing otherwise (no partial modification).
type FullResultSetQueryParamsResultSize = {
  fullResultsSize: number | undefined;
};
type FullResultSetOverrideResultSize =
  | {
      fullResultsSize: number;
      // This is used to generate a more specific error message when the
      // query does not return the expected number of results (from the
      // fullResultsSize parameter).
      notFoundErrorMessage: string | undefined;
    }
  | {
      fullResultsSize: undefined;
    };

type FullResultSetQueryOrOverrideParams =
  | (FullResultSetQueryParams & FullResultSetQueryParamsResultSize)
  | (FullResultSetQueryOverrideParams & FullResultSetOverrideResultSize);
type FullResultSetQueryOrOverrideParamsPartial =
  | (FullResultSetQueryParamsPartial & FullResultSetQueryParamsResultSize)
  | (FullResultSetQueryOverrideParams & FullResultSetOverrideResultSize);

const ParentObjectTypeToNameFilterParam: {
  [K in TableInfoObjectType]?: string;
} = {
  project: "project_name",
  organization: "org_name",
};

function unPartial<T extends {}>(
  params: FullResultSetQueryOrOverrideParamsPartial & T,
  opts: { aclPermission: Permission },
): FullResultSetQueryOrOverrideParams & T {
  if ("fullResultsQueryOverride" in params) {
    return params;
  }
  const { permissionInfo, ...paramsRest } = params;
  if (permissionInfo instanceof Object) {
    return {
      ...paramsRest,
      permissionInfo: {
        ...permissionInfo,
        aclPermission: permissionInfo.aclPermission ?? opts.aclPermission,
      },
    };
  }
  return {
    ...paramsRest,
    permissionInfo,
  };
}

function collectTableInfoObjectTypes({
  priorObjectTables,
  permissionInfo,
}: {
  priorObjectTables?: TableInfoObjectType[];
  permissionInfo: PartialPermissionInfo;
}): [TableInfoObjectType, ...TableInfoObjectType[]] {
  const ret: TableInfoObjectType[] = priorObjectTables ?? [];
  if (isPartialAclPermissionInfo(permissionInfo)) {
    const { parentAclObjectTypes } = aclSpecs[permissionInfo.aclObjectType];
    ret.push(permissionInfo.aclObjectType, ...parentAclObjectTypes);
  }
  if (ret.length === 0) {
    throw new Error("Impossible");
  }
  return [ret[0], ...ret.slice(1)];
}

export function makeFullResultSetQuery(params: FullResultSetQueryParams): {
  query: string;
  queryParams: SqlQueryParams;
  notFoundErrorMessage: string;
} {
  const filters = params.filters ?? {};
  const queryParams = params.startingParams ?? new SqlQueryParams();
  const tableInfoObjectTypes = collectTableInfoObjectTypes({
    priorObjectTables: params.priorObjectTables,
    permissionInfo: params.permissionInfo,
  });
  if (params.isSysadminAutomationLookup) {
    if (
      !(
        typeof params.permissionInfo === "object" &&
        params.permissionInfo.aclObjectType === "project" &&
        params.permissionInfo.aclPermission === "read"
      )
    ) {
      throw new Error(
        `expected sysadmin retention lookup to be limited to project read permissions but got: ${JSON.stringify(params.permissionInfo)}`,
      );
    }
  }

  const query = ((): string => {
    const baseTableInfo = resolvePossiblyVirtualTableInfo(
      tableInfoObjectTypes[0],
    );

    const isSingleObjectFilter = (() => {
      if (hasSingleFilter(filters, "id", params.filterOpts)) {
        return true;
      }
      if (
        hasSingleFilter(filters, "name", params.filterOpts) &&
        tableInfoObjectTypes
          .slice(1)
          .filter((p) => p in ParentObjectTypeToNameFilterParam)
          .every((p) =>
            hasSingleFilter(
              filters,
              ParentObjectTypeToNameFilterParam[p] ?? "",
              params.filterOpts,
            ),
          )
      ) {
        return true;
      }
      return false;
    })();

    const orgMemberObjectsOnly = (() => {
      if (params.permissionInfo === "org-membership") {
        return true;
      }
      if (params.isSysadminAutomationLookup) {
        return false;
      }
      // If the user is not filtering for a single object, they can only list
      // out objects which belong to orgs they are a member of.
      return !isSingleObjectFilter;
    })();

    const aclCheckFilter =
      params.permissionInfo instanceof Object &&
      !params.isSysadminAutomationLookup
        ? `exists (
              select 1 from _expanded_acls where
              ${aclCheckConditions({
                queryParams,
                user_id: params.authLookup.user_id,
                allowAnonAccess: isSingleObjectFilter,
                permission: params.permissionInfo.aclPermission,
                baseObjectType: params.permissionInfo.aclObjectType,
                parentObjectTypes:
                  aclSpecs[params.permissionInfo.aclObjectType]
                    .parentAclObjectTypes,
                overrideRestrictObjectType:
                  params.permissionInfo.overrideRestrictObjectType,
              })}
              ${
                params.permissionInfo.aclCheckAdditionalRowsQuery
                  ? `union all ${params.permissionInfo.aclCheckAdditionalRowsQuery}`
                  : ""
              }
            )`
        : "true";

    const projection = params.fullResultSetSubsetProjection
      ? Array.from(params.fullResultSetSubsetProjection).map(
          (col) => `${makeTableIdentifier(baseTableInfo)}.${doubleQuote(col)}`,
        )
      : [`${makeTableIdentifier(baseTableInfo)}.*`];
    if (params.fullResultSetAdditionalProjections) {
      projection.push(...params.fullResultSetAdditionalProjections);
    }
    if (projection.length === 0) {
      throw new Error("Empty projection");
    }

    return `
      select ${projection.join(",\n")}
      from ${fromTablesClause(tableInfoObjectTypes)}
      where
          true
          and ${genQueryFilters(baseTableInfo, queryParams, filters, params.authLookup, params.filterOpts)}
          and ${aclCheckFilter}
          ${
            !params.consider_deleted
              ? `and ${isActiveChecks(tableInfoObjectTypes)}`
              : ""
          }
          ${
            orgMemberObjectsOnly
              ? `and (${[
                  isOrgLinkageOptional(baseTableInfo.optionalLinkageSpec)
                    ? "organizations.id is null"
                    : "false",
                  `exists (
                        select 1 from members
                        where org_id = organizations.id
                        and user_id = ${queryParams.add(params.authLookup.user_id)}
                    )`,
                ].join(" or ")})`
              : ""
          }
      `;
  })();

  const notFoundPermission = (() => {
    if (isAclPermissionInfo(params.permissionInfo)) {
      return params.permissionInfo.aclPermission;
    } else if (params.permissionInfo === "org-membership") {
      return "org-membership";
    } else {
      const x: never = params.permissionInfo;
      throw new Error(`Unknown permission info: ${JSON.stringify(x)}`);
    }
  })();
  const notFoundErrorMessage = formatNotFoundErrorMessage({
    permission: notFoundPermission,
    objectType: tableInfoObjectTypes[0],
    objectIds: filters.name
      ? queryFilterValueTyped(filters.name)
      : filters.id
        ? queryFilterValueTyped(filters.id)
        : undefined,
  });

  return {
    query,
    queryParams,
    notFoundErrorMessage,
  };
}

function makeFullResultSetQueryOrOverride(
  params: FullResultSetQueryOrOverrideParams,
): {
  query: string;
  queryParams: SqlQueryParams;
  notFoundErrorMessage: string | undefined;
} {
  if ("fullResultsQueryOverride" in params) {
    return {
      query: params.fullResultsQueryOverride,
      queryParams: params.startingParams ?? new SqlQueryParams(),
      notFoundErrorMessage:
        "notFoundErrorMessage" in params
          ? params.notFoundErrorMessage
          : undefined,
    };
  } else {
    return makeFullResultSetQuery(params);
  }
}

export const paginationParamsSchema = z.object({
  limit: appLimitParamSchema.nullish(),
  starting_after: startingAfterSchema.nullish(),
  ending_before: endingBeforeSchema.nullish(),
});

export type PaginationParams = {
  paginationParams?: z.infer<typeof paginationParamsSchema>;
};

function checkPaginationParams({ paginationParams: params }: PaginationParams) {
  if (!isEmpty(params?.starting_after) && !isEmpty(params?.ending_before)) {
    throw new HTTPError(
      400,
      "Cannot specify both starting_after and ending_before",
    );
  }
}

export function splitPaginationParams<T extends {}>(
  x: PaginationParams["paginationParams"] & T,
) {
  const { limit, starting_after, ending_before, ...filters } = x;
  return {
    paginationParams: { limit, starting_after, ending_before },
    filters,
  };
}

export type AdditionalProjectionsParam = {
  finalResultSetAdditionalProjections?: (baseTableName: string) => string[];
};

export function makeGetObjectsQuery(
  params: FullResultSetQueryOrOverrideParamsPartial &
    PaginationParams &
    AdditionalProjectionsParam,
): {
  query: string;
  queryParams: SqlQueryParams;
  notFoundErrorMessage: string | undefined;
} {
  checkPaginationParams(params);
  const { paginationParams } = params;

  const unPartialParams = unPartial(params, { aclPermission: "read" });
  const {
    query: fullResultsSubSubquery,
    queryParams,
    notFoundErrorMessage,
  } = makeFullResultSetQueryOrOverride(unPartialParams);
  const fullResultsSubquery = `
    full_results as (
      select
          t.*,
          row(t.created, t.id)::_created_and_id _key
      from (${fullResultsSubSubquery}) t
    )`;

  const filteredResultsSubquery = (() => {
    // Note that the filters on starting_after/ending_before will look backwards
    // because we are sorting in reverse creation date order.
    if (!isEmpty(paginationParams?.starting_after)) {
      return `
        starting_after_row as (
            select row(created, id)::_created_and_id _filter_key
            from full_results where full_results.id = ${queryParams.add(
              paginationParams?.starting_after,
            )}
        )
        , filtered_results as (
            select full_results.*
            from full_results cross join starting_after_row
            where full_results._key < starting_after_row._filter_key
        )`;
    } else if (!isEmpty(paginationParams?.ending_before)) {
      return `
        ending_before_row as (
            select row(created, id)::_created_and_id _filter_key
            from full_results where full_results.id = ${queryParams.add(
              paginationParams?.ending_before,
            )}
        ), filtered_results as (
            select full_results.*
            from full_results cross join ending_before_row
            where full_results._key > ending_before_row._filter_key
        )`;
    } else {
      return `filtered_results as (select * from full_results)`;
    }
  })();

  const paginatedSubquery = (() => {
    if (isEmpty(paginationParams?.limit)) {
      return `paginated_results as (select * from filtered_results)`;
    } else {
      const sortOrder = isEmpty(paginationParams?.ending_before)
        ? "desc"
        : "asc";
      return `
        paginated_results as (
            select filtered_results.*
            from filtered_results
            order by filtered_results._key ${sortOrder}
            limit ${queryParams.add(paginationParams?.limit)}
        )`;
    }
  })();

  const finalProjection = ["*"].concat(
    params.finalResultSetAdditionalProjections?.("paginated_results") ?? [],
  );
  const query = `
      with
          ${fullResultsSubquery}
          , ${filteredResultsSubquery}
          , ${paginatedSubquery}
      select ${finalProjection.join(", ")} from paginated_results
      order by _key desc
    `;
  return {
    query,
    queryParams,
    notFoundErrorMessage,
  };
}

export async function getObjects(
  params: FullResultSetQueryOrOverrideParamsPartial &
    PaginationParams &
    AdditionalProjectionsParam,
  client?: BtPgClient,
): Promise<Record<string, unknown>[]> {
  const { query, queryParams, notFoundErrorMessage } =
    makeGetObjectsQuery(params);
  const supabase = client ?? getServiceRoleSupabase();
  try {
    //console.log("Running query\n", substituteParamsDebug(query, queryParams.params));
    const { rows: rowsRaw } = await supabase.query(query, queryParams.params);
    const rows = (rowsRaw ?? []).map((r) =>
      Object.fromEntries(Object.entries(r).filter((e) => e[0] !== "_key")),
    );
    if (
      params.fullResultsSize !== undefined &&
      !(rows?.length === params.fullResultsSize)
    ) {
      handleFullResultsMismatch({
        fullResultsSize: params.fullResultsSize,
        rowsSize: rows.length,
        notFoundErrorMessage,
      });
    }
    return rows;
  } catch (error) {
    if (error instanceof HTTPError) {
      throw error;
    } else {
      console.error(`Failed to get objects`, error);
      throw new HTTPError(500, `Failed to get objects`);
    }
  }
}

function inferObjectTable(
  params: FullResultSetQueryOrOverrideParamsPartial,
): TableInfoObjectType {
  const objectTable =
    "fullResultsQueryOverride" in params
      ? params.baseTableOverride
      : collectTableInfoObjectTypes({
          priorObjectTables: params.priorObjectTables,
          permissionInfo: params.permissionInfo,
        })[0];
  if (!objectTable) {
    throw new Error("Override-type query must provide baseTableOverride");
  }
  return objectTable;
}

// NOTE: this function will actually delete rows from the DB, instead of marking
// the row "deleted_at". If you want the latter, use `setDeletedAtObjects` or
// use the more-general `patchObjects`.
export async function deleteObjects(
  params: FullResultSetQueryOrOverrideParamsPartial &
    AdditionalProjectionsParam,
  client?: BtPgClient,
): Promise<unknown[]> {
  const supabase = client ?? getServiceRoleSupabase();
  const unPartialParams = unPartial(params, { aclPermission: "delete" });
  const {
    query: fullResultsSubquery,
    queryParams,
    notFoundErrorMessage,
  } = makeFullResultSetQueryOrOverride(unPartialParams);
  const objectTableInfo = resolveNonVirtualTableInfo(inferObjectTable(params));
  try {
    const returningProjection = ["*"].concat(
      params.finalResultSetAdditionalProjections?.(
        makeTableIdentifier(objectTableInfo),
      ) ?? [],
    );
    const query = `
        with
        full_results as (
        ${fullResultsSubquery}
        )
        delete
        from ${makeTableIdentifier(objectTableInfo)}
        where
            ${
              params.fullResultsSize !== undefined
                ? `(select count(*) = ${queryParams.add(
                    params.fullResultsSize,
                  )} from full_results)`
                : "true"
            }
            and id in (select id from full_results)
        returning ${returningProjection.join(", ")}
    `;
    const { rows } = await supabase.query(query, queryParams.params);
    if (
      params.fullResultsSize !== undefined &&
      !(rows?.length === params.fullResultsSize)
    ) {
      handleFullResultsMismatch({
        fullResultsSize: params.fullResultsSize,
        rowsSize: rows.length,
        notFoundErrorMessage,
      });
    }
    return rows ?? [];
  } catch (error) {
    if (error instanceof HTTPError) {
      throw error;
    } else {
      console.error(`Failed to delete objects`, error);
      throw new HTTPError(500, `Failed to delete objects`);
    }
  }
}

export type PatchParams = {
  patchValueParams?: Record<string, unknown>;
  patchJsonValueParams?: Record<string, unknown>;
  patchExpressions?: Record<string, string>;
  patchJsonExpressions?: Record<string, string>;
  noCoalescePatchValueParams?: Record<string, unknown>;
};

export async function patchObjects(
  params: FullResultSetQueryOrOverrideParamsPartial &
    PatchParams &
    AdditionalProjectionsParam,
  client?: BtPgClient,
): Promise<unknown[]> {
  const supabase = client ?? getServiceRoleSupabase();
  const unPartialParams = unPartial(params, { aclPermission: "update" });
  const {
    query: fullResultsSubquery,
    queryParams,
    notFoundErrorMessage,
  } = makeFullResultSetQueryOrOverride(unPartialParams);
  const objectTableName = makeTableIdentifier(
    resolveNonVirtualTableInfo(inferObjectTable(params)),
  );
  const updateClauses = [];
  for (const [key, val] of Object.entries(params.patchValueParams ?? {})) {
    updateClauses.push(
      `${key} = coalesce(${queryParams.add(val)}, ${objectTableName}.${key})`,
    );
  }
  for (const [key, val] of Object.entries(params.patchJsonValueParams ?? {})) {
    updateClauses.push(
      `${key} = jsonb_recursive_merge(${objectTableName}.${key}, coalesce(${queryParams.add(
        val,
      )}, '{}')::jsonb)`,
    );
  }
  for (const [key, expr] of Object.entries(params.patchExpressions ?? {})) {
    updateClauses.push(`${key} = coalesce(${expr}, ${objectTableName}.${key})`);
  }
  for (const [key, expr] of Object.entries(params.patchJsonExpressions ?? {})) {
    updateClauses.push(
      `${key} = jsonb_recursive_merge(${objectTableName}.${key}, coalesce(${expr}, '{}')::jsonb)`,
    );
  }
  for (const [key, val] of Object.entries(
    params.noCoalescePatchValueParams ?? {},
  )) {
    updateClauses.push(`${key} = ${queryParams.add(val)}`);
  }

  try {
    const returningProjection = ["*"].concat(
      params.finalResultSetAdditionalProjections?.(objectTableName) ?? [],
    );
    const query = (() => {
      if (updateClauses.length === 0) {
        return `
          with
          full_results as (
          ${fullResultsSubquery}
          )
          select ${returningProjection.join(", ")}
          from full_results as ${objectTableName}
          where
            ${
              params.fullResultsSize !== undefined
                ? `(select count(*) = ${queryParams.add(
                    params.fullResultsSize,
                  )} from full_results)`
                : "true"
            }
        `;
      }
      return `
        with
        full_results as (
        ${fullResultsSubquery}
        )
        update ${objectTableName}
        set ${updateClauses.join(",\n")}
        where
            ${
              params.fullResultsSize !== undefined
                ? `(select count(*) = ${queryParams.add(
                    params.fullResultsSize,
                  )} from full_results)`
                : "true"
            }
            and id in (select id from full_results)
        returning ${returningProjection.join(", ")}
    `;
    })();
    // console.log(
    //   "Running query\n",
    //   substituteParamsDebug(query, queryParams.params),
    // );
    const { rows } = await supabase.query(query, queryParams.params);
    if (
      params.fullResultsSize !== undefined &&
      !(rows?.length === params.fullResultsSize)
    ) {
      handleFullResultsMismatch({
        fullResultsSize: params.fullResultsSize,
        rowsSize: rows.length,
        notFoundErrorMessage,
      });
    }
    return rows ?? [];
  } catch (error) {
    if (error instanceof HTTPError) {
      throw error;
    } else if (isObject(error) && error.code === "23505") {
      throw new HTTPError(
        400,
        `Unique key violation: conflicting objects already exist`,
      );
    } else {
      console.error(error);
      throw new HTTPError(500, `Failed to update objects`);
    }
  }
}

export function validatePatchedRows<T>(
  rows: unknown[],
  schema: z.ZodSchema<T>,
): T[] {
  const arraySchema = schema.array();
  const parseResult = arraySchema.safeParse(rows);

  if (!parseResult.success) {
    throw new HTTPError(
      400,
      `Patch would result in invalid data: ${parseResult.error.message}`,
    );
  }

  return parseResult.data;
}

// A wrapper over patchObjects which additionally zod-validates the output row
// before committing the update. This can be useful when the zod schema includes
// cross-field constraints that are difficult to validate at input time.
export async function patchObjectsWithValidation<T>(
  params: FullResultSetQueryOrOverrideParamsPartial &
    PatchParams &
    AdditionalProjectionsParam & {
      validationSchema: z.ZodSchema<T>;
    },
  client?: BtPgClient,
): Promise<T[]> {
  // If a client is provided, use it directly (assumes transaction is already started)
  if (client) {
    const rows = await patchObjects(params, client);
    return validatePatchedRows(rows, params.validationSchema);
  }

  // Otherwise, create a new connection and transaction
  const supabase = getServiceRoleSupabase();
  const pgClient = await supabase.connect();

  try {
    // Start transaction
    await pgClient.query("BEGIN");

    // Perform the patch operation
    const rows = await patchObjects(params, pgClient);

    // Validate the results before committing
    const validatedRows = validatePatchedRows(rows, params.validationSchema);

    // Commit transaction if everything is valid
    await pgClient.query("COMMIT");

    return validatedRows;
  } catch (error) {
    // Rollback on any error
    await pgClient.query("ROLLBACK");
    throw error;
  } finally {
    // Release the connection
    await pgClient.end();
  }
}

export async function setDeletedAtObjects(
  params: FullResultSetQueryOrOverrideParamsPartial &
    AdditionalProjectionsParam,
  client?: BtPgClient,
): Promise<unknown[]> {
  const objectTable = inferObjectTable(params);
  const tableInfo = resolveNonVirtualTableInfo(objectTable);
  if (!tableInfo.deletedAtCol) {
    throw new Error(`Cannot set deletedAt for object type ${objectTable}`);
  }
  return await patchObjects(
    {
      ...unPartial(params, { aclPermission: "delete" }),
      patchExpressions: { [tableInfo.deletedAtCol]: "now()" },
    },
    client,
  );
}

export function extractSingularRow<T>({
  rows,
  notFoundErrorMessage,
}: {
  rows: T[];
  notFoundErrorMessage: string | FormatNotFoundErrorMessageInput | undefined;
}) {
  if (rows.length === 1) {
    return rows[0];
  }
  if (notFoundErrorMessage === undefined) {
    throw new HTTPError(500, "Internal error");
  } else if (typeof notFoundErrorMessage === "string") {
    throw new HTTPError(400, notFoundErrorMessage);
  } else {
    throw new HTTPError(400, formatNotFoundErrorMessage(notFoundErrorMessage));
  }
}

function handleFullResultsMismatch({
  fullResultsSize,
  rowsSize,
  notFoundErrorMessage,
}: {
  fullResultsSize: number;
  rowsSize: number;
  notFoundErrorMessage: string | undefined;
}) {
  console.error(`Expected ${fullResultsSize} results, got ${rowsSize}`);
  if (notFoundErrorMessage) {
    throw new HTTPError(400, notFoundErrorMessage);
  } else {
    throw new HTTPError(500, "Internal error");
  }
}

// Common filter param schemas.

export const idParamSchema = z.object({
  id: z.string().uuid(),
});

export type IdParam = z.infer<typeof idParamSchema>;

// Converts a single query parameter into an array and passes arrays through.
export function preprocessSingleOrArrayParam(x: unknown) {
  if (isEmpty(x)) {
    return x;
  } else if (Array.isArray(x)) {
    if (x.length === 0) {
      throw new HTTPError(400, "Cannot provide empty array filter");
    }
    return x;
  } else {
    return [x];
  }
}

// Applies preprocessSingleOrArrayParam to every member of a zod object schema.
export function preprocessSingleOrArraySchema<
  T extends z.ZodRawShape,
  UnknownKeys extends z.UnknownKeysParam,
  Catchall extends z.ZodTypeAny,
>(object: z.ZodObject<T, UnknownKeys, Catchall>) {
  type ZodOutputType<T extends z.ZodTypeAny> =
    T extends z.ZodType<infer X> ? X : never;
  type ZodArrayType<T extends z.ZodTypeAny> = z.ZodEffects<
    z.ZodArray<T, "atleastone">,
    [ZodOutputType<T>, ...ZodOutputType<T>[]],
    unknown
  >;
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  return new z.ZodObject({
    ...object._def,
    shape: () =>
      Object.fromEntries(
        Object.entries(object.shape).map(([k, v]) => [
          k,
          z.preprocess(preprocessSingleOrArrayParam, v.array().nonempty()),
        ]),
      ),
  }) as z.ZodObject<
    { [k in keyof T]: ZodArrayType<T[k]> },
    UnknownKeys,
    Catchall
  >;
}

export function nullishSingleOrArraySchema<
  T extends z.ZodRawShape,
  UnknownKeys extends z.UnknownKeysParam,
  Catchall extends z.ZodTypeAny,
>(object: z.ZodObject<T, UnknownKeys, Catchall>) {
  return objectNullish(preprocessSingleOrArraySchema(object));
}

const jsonbPathFilterSchemaBase = z
  .record(z.unknown())
  .transform((data) => new JsonbPathEqualityFilter(data));

// Since we sometimes get these params from GET requests, the dictionary may be
// presented as a JSON-serialized string, so we try handling that in the parser.
export const jsonbPathFilterSchema = z.union([
  jsonbPathFilterSchemaBase,
  z.string().transform((data) => {
    return jsonbPathFilterSchemaBase.parse(JSON.parse(data));
  }),
]);

export const commonFilterParamsSchema = nullishSingleOrArraySchema(
  z.object({
    name: z.string(),
    project_name: z.string(),
    project_id: z.string(),
    org_name: z.string(),
    org_id: z.string(),
    id: idParamSchema.shape.id,
  }),
);

export function makeUserAuthIdFilter(auth_id: string) {
  return new CustomQueryFilter({
    values: [auth_id],
    tableName: "users",
    col: "auth_id",
  });
}
