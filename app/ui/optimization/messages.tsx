import {
  type UserConsentConfirmationData,
  type TaskEditConfirmationData,
  type DatasetEditConfirmationData,
  type EditScorersConfirmationData,
  type CreateLLMScorerConfirmationData,
  type CreateCodeScorerConfirmationData,
} from "#/utils/optimization/provider";
import {
  type ParsedMessage,
  type ToolInteraction,
  type SystemMessage,
} from "./use-global-chat-context";
import { ContextObjectBadge } from "#/ui/optimization/context-object-badge";
import { type ContextObject } from "#/ui/optimization/use-global-chat-context";
import { type UserMessage as UserMessageType } from "./use-global-chat-context";
import { MarkdownViewer } from "#/ui/markdown";
import { SyntaxHighlight } from "#/ui/syntax-highlighter";
import { ErrorBanner } from "#/ui/error-banner";
import { InfoBanner } from "#/ui/info-banner";
import { Feedback } from "#/ui/optimization/feedback";
import {
  EditTaskToolDisplay,
  EditDataToolDisplay,
  EditScorersToolDisplay,
  RunTaskToolDisplay,
  DetailedResultsToolDisplay,
  SummaryToolDisplay,
  AvailableScorersToolDisplay,
  UserConsentMessage,
  CollapsibleAction,
  Title,
  // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
  // EditBTQLToolDisplay,
  CreateLLMScorerToolDisplay,
  CreateCodeScorerToolDisplay,
  InferSchemaToolDisplay,
  BTQLQueryToolDisplay,
  SearchDocsToolDisplay,
} from "#/ui/optimization/tool-message-ui";
import { type ChatContext, type PageKey } from "@braintrust/local/optimization";
import { ObjectRowLink, evalRowUrlTransform } from "./object-row-link";
import { cn } from "#/utils/classnames";
import { Button } from "#/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";

export const Messages = ({
  parsedMessages,
  editTaskConfirmationData,
  setEditTaskConfirmationData,
  editDatasetConfirmationData,
  setEditDatasetConfirmationData,
  userConsentConfirmationData,
  setUserConsentConfirmationData,
  editScorersConfirmationData,
  setEditScorersConfirmationData,
  createLLMScorerConfirmationData,
  setCreateLLMScorerConfirmationData,
  createCodeScorerConfirmationData,
  setCreateCodeScorerConfirmationData,
  setAllowRunningWithoutConsent,
  allowRunningWithoutConsent,
  pageKey,
  setUserMessage,
  handleSendMessage,
  chat,
  hasMultipleSelectedExperiments = false,
  size = "widget",
  onBTQLFilter,
}: {
  parsedMessages: ParsedMessage[];
  editTaskConfirmationData: TaskEditConfirmationData | null;
  setEditTaskConfirmationData: (
    editTaskConfirmationData: TaskEditConfirmationData | null,
  ) => void;
  editDatasetConfirmationData: DatasetEditConfirmationData | null;
  setEditDatasetConfirmationData: (
    editDatasetConfirmationData: DatasetEditConfirmationData | null,
  ) => void;
  userConsentConfirmationData: UserConsentConfirmationData | null;
  setUserConsentConfirmationData: (
    userConsentConfirmationData: UserConsentConfirmationData | null,
  ) => void;
  editScorersConfirmationData: EditScorersConfirmationData | null;
  setEditScorersConfirmationData: (
    editScorersConfirmationData: EditScorersConfirmationData | null,
  ) => void;
  createLLMScorerConfirmationData: CreateLLMScorerConfirmationData | null;
  setCreateLLMScorerConfirmationData: (
    createLLMScorerConfirmationData: CreateLLMScorerConfirmationData | null,
  ) => void;
  createCodeScorerConfirmationData: CreateCodeScorerConfirmationData | null;
  setCreateCodeScorerConfirmationData: (
    createCodeScorerConfirmationData: CreateCodeScorerConfirmationData | null,
  ) => void;
  setAllowRunningWithoutConsent: (allow: boolean) => void;
  allowRunningWithoutConsent: boolean;
  pageKey: PageKey;
  setUserMessage: (userMessage: string) => void;
  handleSendMessage: (
    userMessage: UserMessageType,
    options?: {
      clearContextObjects?: boolean;
      clearUserMessage?: boolean;
    },
  ) => Promise<void>;
  chat: ChatContext | null;
  hasMultipleSelectedExperiments?: boolean;
  size?: "widget" | "full";
  onBTQLFilter?: (filterText: string) => void;
}) => {
  return (
    <>
      {parsedMessages.map((msg, index) => (
        <div key={msg.id} className="mb-2">
          {msg.type === "tool_interaction" && msg.toolCallId && (
            <RenderToolMessage
              msg={msg}
              editTaskConfirmationData={editTaskConfirmationData}
              setEditTaskConfirmationData={setEditTaskConfirmationData}
              editDatasetConfirmationData={editDatasetConfirmationData}
              setEditDatasetConfirmationData={setEditDatasetConfirmationData}
              userConsentConfirmationData={userConsentConfirmationData}
              setUserConsentConfirmationData={setUserConsentConfirmationData}
              setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
              allowRunningWithoutConsent={allowRunningWithoutConsent}
              editScorersConfirmationData={editScorersConfirmationData}
              setEditScorersConfirmationData={setEditScorersConfirmationData}
              createCodeScorerConfirmationData={
                createCodeScorerConfirmationData
              }
              setCreateCodeScorerConfirmationData={
                setCreateCodeScorerConfirmationData
              }
              createLLMScorerConfirmationData={createLLMScorerConfirmationData}
              setCreateLLMScorerConfirmationData={
                setCreateLLMScorerConfirmationData
              }
              pageKey={pageKey}
              onBTQLFilter={onBTQLFilter}
            />
          )}
          {msg.type === "llm_message" && (
            <MarkdownViewer
              className={cn("px-2 py-0 text-xs", size === "full" && "text-sm")}
              value={msg.llmContent}
              components={{
                a: ObjectRowLink,
              }}
              urlTransform={evalRowUrlTransform}
            />
          )}
          {msg.type === "system_message" && <SystemMessage msg={msg} />}
          {msg.type === "user_message" && <UserMessage msg={msg} size={size} />}
          {(msg.type === "tool_interaction" ||
            msg.type === "llm_message" ||
            msg.type === "system_message") &&
            msg.isLastMessageOfTurn &&
            chat && (
              <Feedback
                sendFeedback={chat.recordFeedback.bind(chat)}
                isActive={index === parsedMessages.length - 1}
              />
            )}
        </div>
      ))}
    </>
  );
};

const RenderToolMessage = ({
  msg,
  editTaskConfirmationData,
  setEditTaskConfirmationData,
  editDatasetConfirmationData,
  setEditDatasetConfirmationData,
  userConsentConfirmationData,
  setUserConsentConfirmationData,
  setAllowRunningWithoutConsent,
  allowRunningWithoutConsent,
  editScorersConfirmationData,
  setEditScorersConfirmationData,
  createLLMScorerConfirmationData,
  setCreateLLMScorerConfirmationData,
  createCodeScorerConfirmationData,
  setCreateCodeScorerConfirmationData,
  pageKey,
  onBTQLFilter,
}: {
  msg: ToolInteraction;
  editTaskConfirmationData: TaskEditConfirmationData | null;
  setEditTaskConfirmationData: (
    editTaskConfirmationData: TaskEditConfirmationData | null,
  ) => void;
  editDatasetConfirmationData: DatasetEditConfirmationData | null;
  setEditDatasetConfirmationData: (
    editDatasetConfirmationData: DatasetEditConfirmationData | null,
  ) => void;
  userConsentConfirmationData: UserConsentConfirmationData | null;
  setUserConsentConfirmationData: (
    userConsentConfirmationData: UserConsentConfirmationData | null,
  ) => void;
  setAllowRunningWithoutConsent: (allow: boolean) => void;
  allowRunningWithoutConsent: boolean;
  editScorersConfirmationData: EditScorersConfirmationData | null;
  setEditScorersConfirmationData: (
    editScorersConfirmationData: EditScorersConfirmationData | null,
  ) => void;
  createLLMScorerConfirmationData: CreateLLMScorerConfirmationData | null;
  setCreateLLMScorerConfirmationData: (
    createLLMScorerConfirmationData: CreateLLMScorerConfirmationData | null,
  ) => void;
  createCodeScorerConfirmationData: CreateCodeScorerConfirmationData | null;
  setCreateCodeScorerConfirmationData: (
    createCodeScorerConfirmationData: CreateCodeScorerConfirmationData | null,
  ) => void;
  pageKey: PageKey;
  onBTQLFilter?: (filterText: string) => void;
}) => {
  switch (msg.functionName) {
    case "edit_task":
      return (
        <EditTaskToolDisplay
          msg={msg}
          editTaskConfirmationData={editTaskConfirmationData}
          setEditTaskConfirmationData={setEditTaskConfirmationData}
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );
    case "edit_data":
      return (
        <EditDataToolDisplay
          msg={msg}
          editDatasetConfirmationData={editDatasetConfirmationData}
          setEditDatasetConfirmationData={setEditDatasetConfirmationData}
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );
    case "edit_scorers":
      return (
        <EditScorersToolDisplay
          msg={msg}
          editScorersConfirmationData={editScorersConfirmationData}
          setEditScorersConfirmationData={setEditScorersConfirmationData}
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );
    case "create_code_scorer":
      return (
        <CreateCodeScorerToolDisplay
          msg={msg}
          createCodeScorerConfirmationData={createCodeScorerConfirmationData}
          setCreateCodeScorerConfirmationData={
            setCreateCodeScorerConfirmationData
          }
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );
    case "infer_schema":
      return <InferSchemaToolDisplay msg={msg} />;
    case "btql_query":
      return <BTQLQueryToolDisplay msg={msg} onBTQLFilter={onBTQLFilter} />;
    case "create_llm_scorer":
      return (
        <CreateLLMScorerToolDisplay
          msg={msg}
          createLLMScorerConfirmationData={createLLMScorerConfirmationData}
          setCreateLLMScorerConfirmationData={
            setCreateLLMScorerConfirmationData
          }
          selectedContinueWithoutConsent={allowRunningWithoutConsent}
          setSelectedContinueWithoutConsent={setAllowRunningWithoutConsent}
        />
      );
    case "search_docs":
      return <SearchDocsToolDisplay msg={msg} />;
    case "run_task":
      return (
        <RunTaskToolDisplay
          msg={msg}
          userConsentConfirmationData={userConsentConfirmationData}
          setUserConsentConfirmationData={setUserConsentConfirmationData}
          setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
          allowRunningWithoutConsent={allowRunningWithoutConsent}
        />
      );
    case "get_results":
      return <DetailedResultsToolDisplay msg={msg} pageKey={pageKey} />;
    case "get_summary":
      return <SummaryToolDisplay msg={msg} />;
    case "continue_execution":
      if (msg.status === "pending_output" && userConsentConfirmationData) {
        return (
          <UserConsentMessage
            userConsentConfirmationData={userConsentConfirmationData}
            setUserConsentConfirmationData={setUserConsentConfirmationData}
            setAllowRunningWithoutConsent={setAllowRunningWithoutConsent}
            allowRunningWithoutConsent={allowRunningWithoutConsent}
          />
        );
      }
      return null;
    case "get_available_scorers":
      return <AvailableScorersToolDisplay msg={msg} />;

    // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
    // case "edit_btql":
    //   return <EditBTQLToolDisplay msg={msg} />;
    default:
      break;
  }

  // Default case for all other function names and statuses
  return (
    <CollapsibleAction title={<Title msg={msg} />} defaultCollapsed={true}>
      <SyntaxHighlight
        language="json"
        className="break-all rounded-b-md border-x border-b p-2 bg-primary-100"
        content={JSON.stringify(
          msg.status === "completed" ||
            msg.status === "error_executing_tool" ||
            msg.status === "rejected"
            ? msg.toolOutput
            : msg.arguments,
          null,
          2,
        )}
      />
    </CollapsibleAction>
  );
};

const SystemMessage = ({ msg }: { msg: SystemMessage }) => {
  if (msg.variant === "error") {
    return (
      <ErrorBanner
        className="items-start overflow-x-auto"
        iconClassName="mt-0.5"
      >
        {msg.message}
      </ErrorBanner>
    );
  }
  return (
    <InfoBanner className="items-start" iconClassName="mt-0.5">
      {msg.message}
    </InfoBanner>
  );
};

const CONTEXT_OBJECT_DISPLAY_LIMIT = 3;

const UserMessage = ({
  msg,
  size = "widget",
}: {
  msg: UserMessageType;
  size?: "widget" | "full";
}) => {
  return (
    <div className="flex min-h-7 flex-col justify-center gap-1 rounded-md border p-2 bg-background border-primary-300 text-primary-900">
      {msg.contextObjects && Object.keys(msg.contextObjects).length > 0 && (
        <ContextObjectBadgeSection contextObjects={msg.contextObjects} />
      )}
      <div className="overflow-hidden whitespace-pre-wrap break-words text-xs">
        {msg.message}
      </div>
    </div>
  );
};

export const ContextObjectBadgeSection = ({
  contextObjects,
  onDelete,
  onClearAll,
}: {
  contextObjects: Record<string, ContextObject>;
  onDelete?: (contextObject: ContextObject) => void;
  onClearAll?: () => void;
}) => {
  const contextObjectsArray = Object.values(contextObjects);

  const hasMore = contextObjectsArray.length > CONTEXT_OBJECT_DISPLAY_LIMIT;
  const displayedObjects = contextObjectsArray.slice(
    0,
    CONTEXT_OBJECT_DISPLAY_LIMIT,
  );
  const restObjects = contextObjectsArray.slice(CONTEXT_OBJECT_DISPLAY_LIMIT);

  return (
    <div className="group mb-1 flex flex-wrap gap-1">
      {displayedObjects.map(
        (contextObjectItem: ContextObject, index: number) => {
          return (
            <ContextObjectBadge
              key={index}
              contextObjectItem={contextObjectItem}
              onDelete={onDelete}
            />
          );
        },
      )}
      {hasMore && (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              size="xs"
              className="flex size-5 min-w-fit items-center rounded-[4px] text-xs font-normal bg-primary-100 text-primary-700"
              variant="border"
            >
              {restObjects.length} +
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="flex w-fit flex-col gap-1 overflow-y-auto p-1 text-xs"
            align="start"
          >
            {restObjects.map(
              (contextObjectItem: ContextObject, index: number) => {
                return (
                  <ContextObjectBadge
                    key={index}
                    contextObjectItem={contextObjectItem}
                    className="border-none bg-background"
                  />
                );
              },
            )}
          </PopoverContent>
        </Popover>
      )}
      {onClearAll && (
        <Button
          size="xs"
          variant="ghost"
          className="hidden h-5 min-w-0 rounded-[4px] text-[11px] font-normal text-primary-500 group-hover:flex"
          onClick={onClearAll}
        >
          Clear
        </Button>
      )}
    </div>
  );
};
