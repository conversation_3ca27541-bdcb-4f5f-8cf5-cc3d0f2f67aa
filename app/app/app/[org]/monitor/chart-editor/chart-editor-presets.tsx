import { ToggleGroup, ToggleGroupItem } from "#/ui/toggle-group";
import { useCallback } from "react";
import { MONITOR_PRESET_CARDS } from "../card-config/monitor-cards.constants";
import { MonitorCardIcon } from "../card/monitor-card-icon";

export const ChartEditorPresets = ({
  selectedPresetId,
  onChange,
}: {
  selectedPresetId?: string;
  onChange: (presetId?: string) => void;
}) => {
  const CustomIcon = MonitorCardIcon("custom-chart");

  const onValueChange = useCallback(
    (presetId: string) => {
      const isValid = [...MONITOR_PRESET_CARDS.keys()].includes(presetId);
      onChange(isValid ? presetId : undefined);
    },
    [onChange],
  );

  return (
    <ToggleGroup
      type="single"
      className="flex flex-wrap justify-start gap-2"
      value={selectedPresetId ?? "custom"}
      onValueChange={onValueChange}
    >
      <ToggleGroupItem
        size="sm"
        value="custom"
        variant="outline"
        className="gap-2 rounded px-3 text-xs hover:bg-primary-100 hover:text-primary-900"
      >
        <CustomIcon className="size-3" />
        Custom chart
      </ToggleGroupItem>
      {[...MONITOR_PRESET_CARDS.entries()].map(([id, card]) => {
        const Icon = MonitorCardIcon(card.header?.iconName ?? "custom-chart");
        return (
          <ToggleGroupItem
            size="sm"
            variant="outline"
            className="gap-2 rounded px-3 text-xs hover:bg-primary-100 hover:text-primary-900"
            value={id}
            key={id}
          >
            <Icon className="size-3" />
            {card.header?.title ?? card.name}
          </ToggleGroupItem>
        );
      })}
    </ToggleGroup>
  );
};
