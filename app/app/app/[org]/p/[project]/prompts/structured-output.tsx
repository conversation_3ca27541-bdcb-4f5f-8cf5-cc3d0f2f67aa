"use client";
import { <PERSON><PERSON> } from "#/ui/button";
import {
  type PropsWithChildren,
  useCallback,
  useState,
  useEffect,
} from "react";
import { ExternalLink } from "#/ui/link";
import { DataTextEditor } from "#/ui/data-text-editor";
import { cn } from "#/utils/classnames";
import { Input, inputClassName } from "#/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import ReactTextareaAutosize from "react-textarea-autosize";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  Form,
  FormMessage,
  FormDescription,
} from "#/ui/form";
import { useForm, useWatch } from "react-hook-form";
import { Switch } from "#/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "#/ui/dialog";
import { responseFormatJsonSchemaSchema } from "@braintrust/typespecs";
import { Ta<PERSON>, TabsList, TabsTrigger } from "#/ui/tabs";

export const structuredOutputSchema = responseFormatJsonSchemaSchema
  .omit({ name: true })
  .extend({
    name: z
      .string()
      .min(1, "name is required")
      .regex(
        /^[a-zA-Z0-9_-]+$/,
        "name must only contain letters, numbers, '_' and '-'",
      ),
  });

export type OnSaveStructuredOutputFn = (
  data: z.infer<typeof structuredOutputSchema>,
) => Promise<void>;

type StructuredOutputProps = PropsWithChildren<{
  onSaveStructuredOutput: OnSaveStructuredOutputFn;
  schema?: z.infer<typeof structuredOutputSchema>;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  isReadOnly?: boolean;
}>;

export const StructuredOutput = ({
  schema,
  onSaveStructuredOutput,
  children,
  isOpen,
  setIsOpen,
  isReadOnly,
}: StructuredOutputProps) => {
  const [schemaTab, setSchemaTab] = useState<"raw" | "template">("raw");
  const [forceRefresh, setForceRefresh] = useState(false);

  const form = useForm<z.infer<typeof structuredOutputSchema>>({
    resolver: zodResolver(structuredOutputSchema),
    defaultValues: {
      name: schema?.name ?? "",
      description: schema?.description ?? "",
      schema: schema?.schema ?? {},
      strict: schema?.strict ?? false,
    },
  });

  const showDescription = useWatch({
    control: form.control,
    name: "name",
  });

  const onSchemaError = useCallback(
    (error: Error | null) => {
      if (error) {
        form.setError("schema", {
          type: "manual",
          message: error.message,
        });
      } else {
        form.clearErrors("schema");
      }
    },
    [form],
  );

  const onSubmit = async (data: z.infer<typeof structuredOutputSchema>) => {
    try {
      await onSaveStructuredOutput(data);
      setIsOpen(false);
    } catch (error) {
      console.error("Failed to save structured output:", error);
    }
  };

  // Reset forceRefresh after it's been used
  // We use a timeout to ensure the value change has been processed by the SchemaBuilder
  // before resetting the flag. This prevents the flag from being reset too early
  // and ensures the SchemaBuilder receives forceRefresh=true when needed.
  useEffect(() => {
    if (forceRefresh) {
      // Use a timeout to ensure the value change has been processed first
      const timer = setTimeout(() => {
        setForceRefresh(false);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [forceRefresh]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen} modal={true}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        onEscapeKeyDown={(e) => {
          e.stopPropagation();
        }}
        className="flex flex-col gap-0 overflow-hidden p-0 sm:max-w-[600px]"
        onPointerDownOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader className="m-0 flex-none border-b p-4 border-primary-100">
          <DialogTitle className="mb-2">Structured output</DialogTitle>
          <DialogDescription>
            {`${isReadOnly ? "The" : "Define the"} JSON schema for the structured output of the prompt`}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-1 flex-col overflow-hidden"
          >
            <div className="flex flex-1 flex-col gap-4 overflow-auto p-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs">Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter name"
                        className="h-9"
                        autoComplete="off"
                        disabled={isReadOnly}
                        {...field}
                        value={field.value ?? ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {showDescription && (
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-xs">Description</FormLabel>
                      <FormControl>
                        <ReactTextareaAutosize
                          className={cn(inputClassName, "py-1.5")}
                          placeholder="Enter description (optional)"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={form.control}
                name="schema"
                render={({ field }) => (
                  <FormItem>
                    {!isReadOnly && (
                      <Tabs
                        value={schemaTab}
                        onValueChange={(value) =>
                          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                          setSchemaTab(value as "raw" | "template")
                        }
                      >
                        <TabsList className="mb-1">
                          <TabsTrigger value="raw">Schema</TabsTrigger>
                          <TabsTrigger value="template">From data</TabsTrigger>
                        </TabsList>
                      </Tabs>
                    )}
                    <FormLabel className="flex justify-between text-xs">
                      {schemaTab === "raw" ? (
                        <>
                          Schema definition
                          {!isReadOnly && (
                            <Button
                              type="button"
                              size="inline"
                              transparent
                              className="text-xs font-normal text-accent-600"
                              onClick={(e) => {
                                e.preventDefault();
                                setForceRefresh(true);
                                field.onChange(SAMPLE, {
                                  shouldValidate: true,
                                  shouldDirty: true,
                                });
                              }}
                            >
                              Insert example
                            </Button>
                          )}
                        </>
                      ) : (
                        <span>
                          Path to schema definition in data (e.g.{" "}
                          <span className="font-mono">
                            {"metadata.structure"}
                          </span>
                          )
                        </span>
                      )}
                    </FormLabel>
                    <FormControl>
                      <div className="flex flex-col gap-2">
                        {schemaTab === "raw" ? (
                          <DataTextEditor
                            autoFocus
                            value={field.value}
                            placeholder="Enter schema"
                            tabAutocomplete={true}
                            formatOnBlur
                            className="pl-1"
                            readOnly={isReadOnly}
                            allowedRenderOptions={[
                              "schema-builder",
                              "json",
                              "yaml",
                            ]}
                            onError={onSchemaError}
                            forceRefresh={forceRefresh}
                            onChange={(value) => {
                              field.onChange(value, {
                                shouldValidate: true,
                                shouldDirty: true,
                              });
                            }}
                          />
                        ) : (
                          <ReactTextareaAutosize
                            className={cn(inputClassName, "py-1.5")}
                            placeholder="Enter path to schema template"
                            value={
                              typeof field.value === "string"
                                ? field.value.replace(/^{{|}}$/g, "")
                                : ""
                            }
                            readOnly={isReadOnly}
                            onChange={(e) => {
                              field.onChange("{{" + e.target.value + "}}", {
                                shouldValidate: true,
                                shouldDirty: true,
                              });
                            }}
                          />
                        )}
                      </div>
                    </FormControl>
                    <FormDescription>
                      This JSON object corresponds to the{" "}
                      <code>response_format.json_schema.schema</code> argument
                      in the{" "}
                      <ExternalLink href="https://platform.openai.com/docs/api-reference/chat/create">
                        OpenAI API
                      </ExternalLink>
                      .
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="strict"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="flex h-8 items-center gap-2 text-xs">
                        <Switch
                          disabled={isReadOnly}
                          className="flex-none data-[state=checked]:bg-primary-700"
                          checked={!!field.value}
                          onCheckedChange={(value) =>
                            field.onChange(value, {
                              shouldDirty: true,
                            })
                          }
                        />
                        Strict mode
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            {!isReadOnly && (
              <DialogFooter className="flex-none border-t p-4 border-primary-100">
                <Button
                  type="submit"
                  variant="primary"
                  size="sm"
                  isLoading={form.formState.isSubmitting}
                  disabled={!form.formState.isDirty}
                >
                  Save
                </Button>
              </DialogFooter>
            )}
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

const SAMPLE = {
  type: "object",
  properties: {
    steps: {
      type: "array",
      items: {
        type: "object",
        properties: {
          explanation: {
            type: "string",
          },
          output: {
            type: "string",
          },
        },
        required: ["explanation", "output"],
        additionalProperties: false,
      },
    },
    final_answer: {
      type: "string",
    },
  },
  required: ["steps", "final_answer"],
  additionalProperties: false,
};
