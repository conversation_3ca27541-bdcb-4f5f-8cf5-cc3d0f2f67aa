use std::{
    path::{Path, PathBuf},
    sync::Arc,
};

use async_util::test_util::TwoWaySyncPointSendAndWait;
use futures::future::BoxFuture;
use tracing::Instrument;
use util::{anyhow::Result, uuid::Uuid};

use crate::{
    clear_compacted_index::{clear_compacted_index_inner, ClearCompactedIndexInnerInput},
    config_with_store::StoreInfo,
    global_locks_manager::GlobalLocksManager,
    global_store::{
        GlobalStore, LastCompactedIndexMeta, LastIndexOperation, LastIndexOperationDetails,
    },
    limits::global_limits,
    status_updater::{StatusUpdate, StatusUpdater},
    tantivy_index::{
        extract_opstamp, flash_tantivy_index, validate_tantivy_index, IndexMetaJson,
        TantivyIndexScope, ValidateTantivyIndexOptions,
    },
};

#[derive(<PERSON><PERSON>, Debug)]
pub struct WriteIndexOperationHarnessInput {
    pub segment_id: Uuid,
    pub index_store: StoreInfo,
    pub schema: util::schema::Schema,
    pub global_store: Arc<dyn GlobalStore>,
    pub locks_manager: Arc<dyn GlobalLocksManager>,
    pub validate_opts: ValidateTantivyIndexOptions,
}

#[derive(Default)]
pub struct WriteIndexOperationHarnessOptionalInput {
    pub use_status_updater: bool,
    pub skip_permit: bool,
    pub try_acquire: bool,
    pub testing_override_missing_del_file_retries: Option<usize>,
    pub sleep_after_acquire_lock_ms: Option<usize>,
    pub testing_sync_points: WriteIndexOperationHarnessTestingSyncPoints,
}

#[derive(Default)]
pub struct WriteIndexOperationHarnessTestingSyncPoints {
    pub after_acquire_index_lock: Option<TwoWaySyncPointSendAndWait>,
}

#[derive(Debug)]
pub struct WriteIndexOperationHarnessTaskAdditionalInput<'a> {
    pub global_store_last_compacted_index_meta: Option<LastCompactedIndexMeta>,
    pub status_updater: &'a Option<StatusUpdater>,
}

// A harness for operations which write to a segment's tantivy index. The harness handles the
// following boilerplate:
//
//   - Manage a status updater for publishing live updates.
//
//   - Acquire resource permit and distributed lock for the segment index.
//
//   - Retry certain failure scenarios, generally due to unavoidable index corruption (see comments
//   below). Note that in some cases we clear the index in order to start from scratch.
pub async fn write_index_operation_harness<F, TaskInput, TaskOptions, TaskOutput>(
    harness_input: WriteIndexOperationHarnessInput,
    harness_optional_input: WriteIndexOperationHarnessOptionalInput,
    task_factory: F,
    task_input: TaskInput,
    task_options: TaskOptions,
    task_noop_output: TaskOutput,
) -> Result<TaskOutput>
where
    TaskInput: Clone,
    TaskOptions: Clone,
    F: for<'a> FnMut(
        TaskInput,
        TaskOptions,
        WriteIndexOperationHarnessTaskAdditionalInput<'a>,
    ) -> BoxFuture<'a, Result<TaskOutput>>,
{
    let status_updater = if harness_optional_input.use_status_updater {
        Some(StatusUpdater::new(
            harness_input.global_store.clone(),
            harness_input.segment_id,
        ))
    } else {
        None
    };

    let output = write_index_operation_harness_inner(
        harness_input,
        harness_optional_input,
        task_factory,
        task_input,
        task_options,
        task_noop_output,
        &status_updater,
    )
    .await;
    if let Err(e) = &output {
        status_updater.update(LastIndexOperation {
            error: Some(e.to_string()),
            ..Default::default()
        });
    }
    status_updater.finish().await?;
    output
}

async fn write_index_operation_harness_inner<'a, F, TaskInput, TaskOptions, TaskOutput>(
    harness_input: WriteIndexOperationHarnessInput,
    harness_optional_input: WriteIndexOperationHarnessOptionalInput,
    mut task_factory: F,
    task_input: TaskInput,
    task_options: TaskOptions,
    task_noop_output: TaskOutput,
    status_updater: &'a Option<StatusUpdater>,
) -> Result<TaskOutput>
where
    TaskInput: Clone,
    TaskOptions: Clone,
    F: for<'b> FnMut(
        TaskInput,
        TaskOptions,
        WriteIndexOperationHarnessTaskAdditionalInput<'b>,
    ) -> BoxFuture<'b, Result<TaskOutput>>,
{
    if !harness_optional_input.skip_permit {
        status_updater.update(LastIndexOperation {
            stage: Some("acquiring permit".to_string()),
            details: Some(LastIndexOperationDetails::Compact { num_wal_entries: 0 }),
            ..Default::default()
        });
    }

    let _permit = if harness_optional_input.skip_permit {
        None
    } else {
        Some(
            global_limits()
                .index_operations
                .start_task()
                .instrument(tracing::info_span!("acquiring permit",))
                .await?,
        )
    };

    let index_scope = TantivyIndexScope::Segment(harness_input.segment_id);

    status_updater.update(LastIndexOperation {
        stage: Some("acquiring lock".to_string()),
        ..Default::default()
    });
    let _lock = if harness_optional_input.try_acquire {
        match harness_input
            .locks_manager
            .try_write(&index_scope.lock_name())
            .await?
        {
            Some(lock) => lock,
            None => {
                tracing::info!(
                    "Could not acquire lock for segment {}",
                    harness_input.segment_id
                );
                status_updater.update(LastIndexOperation {
                    stage: Some("skipping (lock not available)".to_string()),
                    finished: Some(true),
                    estimated_progress: Some(0.0),
                    ..Default::default()
                });

                return Ok(task_noop_output);
            }
        }
    } else {
        harness_input
            .locks_manager
            .write(&index_scope.lock_name())
            .await?
    };
    let _status_updater_guard = status_updater.ensure_next_update();
    status_updater.update(LastIndexOperation {
        stage: Some("acquired lock".to_string()),
        ..Default::default()
    });

    if let Some(duration_ms) = harness_optional_input.sleep_after_acquire_lock_ms {
        tokio::time::sleep(tokio::time::Duration::from_millis(duration_ms as u64)).await;
    }
    if let Some(sync_point) = harness_optional_input
        .testing_sync_points
        .after_acquire_index_lock
    {
        sync_point.send_and_wait().await;
    }

    // In some cases index ops will fail for reasons that warrant a retry:
    //
    // - There are conflicting files lying around on the filesystem which interfere with the
    // compaction process. One specific example we handle is when there is a
    // [segment_id].[opstamp].del file, where the [opstamp] is greater than our stored meta.json.
    // So when we try to run compaction, we may end up re-creating that `.del` file and failing
    // because it already exists. In this case, our solution is to retry, deleting that file before
    // proceeding with the compaction.
    //
    //      - In some cases, we observe this error cropping up even when there are no prior `.del`
    //      files in the index. Which indicates some sort of yet-undiagnosed index corruption or
    //      tantivy bug. So as a fallback, we'll clear the index if we can't get past the error
    //      after the number of retries, and try once more.
    //
    // - The index has been corrupted for other reasons. E.g. we occasionally see `.del` files
    // mentioned in the index metadata, but they no longer exist on the filesystem. In this case,
    // we also recompact and retry.
    let mut wipe_opstamp_file: Option<PathBuf> = None;
    let missing_del_file_max_retries = harness_optional_input
        .testing_override_missing_del_file_retries
        .unwrap_or(2);
    let mut num_missing_del_file_errors = 0;
    let mut tried_recompact_and_retry = false;
    loop {
        // Before any index write operation, we flash the tantivy metadata on the filesystem to the
        // last compacted index metadata. This way, if we happened to try a compaction run which
        // failed partway through, or the metadata changed for any other reason, we can restore the
        // index state to a known valid point, and avoid considering any stray segments from the
        // failed compaction.

        let global_store_last_compacted_index_meta = {
            let segment_id_arr = [harness_input.segment_id];
            let mut segment_metadatas = harness_input
                .global_store
                .query_segment_metadatas(&segment_id_arr)
                .await?;
            segment_metadatas.remove(0).last_compacted_index_meta
        };

        status_updater.update(LastIndexOperation {
            stage: Some("flashing index".to_string()),
            ..Default::default()
        });
        flash_tantivy_index(
            global_store_last_compacted_index_meta
                .as_ref()
                .map(|x| &x.tantivy_meta),
            &harness_input.schema,
            &harness_input.index_store,
            &index_scope,
            wipe_opstamp_file.take().as_deref(),
        )
        .await?;

        let output = task_factory(
            task_input.clone(),
            task_options.clone(),
            WriteIndexOperationHarnessTaskAdditionalInput {
                global_store_last_compacted_index_meta: global_store_last_compacted_index_meta
                    .clone(),
                status_updater,
            },
        )
        .await;

        let output_err = match output {
            Ok(x) => return Ok(x),
            Err(e) => e,
        };

        // If we haven't exceeded the maximum number of deletion file errors, retry with the
        // wipe_opstamp_file set to the stray `.del` file.
        if num_missing_del_file_errors < missing_del_file_max_retries {
            if let Some(path) = extract_file_already_exists_error_path(&output_err) {
                log::warn!(
                      "Encountered existing file {:?}. Retrying with deletion of existing opstamp file",
                      path,
                );
                wipe_opstamp_file = Some(path);
                num_missing_del_file_errors += 1;
                continue;
            }
        }

        // If we haven't yet recompacted-and-retried, and it's a valid error to recompact, try
        // that.
        if !tried_recompact_and_retry {
            let is_file_already_exists_error =
                if let Some(path) = extract_file_already_exists_error_path(&output_err) {
                    log::warn!(
                        "Encountered existing file {:?}. Recompacting and retrying.",
                        path
                    );
                    true
                } else {
                    false
                };
            if is_file_already_exists_error
                || should_recompact_invalid_segment(
                    harness_input.segment_id,
                    &harness_input.index_store,
                    global_store_last_compacted_index_meta
                        .as_ref()
                        .map(|x| &x.tantivy_meta)
                        .cloned(),
                    &harness_input.validate_opts,
                )
                .await?
            {
                tried_recompact_and_retry = true;
                clear_compacted_index_inner(ClearCompactedIndexInnerInput {
                    segment_id: harness_input.segment_id,
                    global_store: harness_input.global_store.clone(),
                    current_last_compacted_index_meta: global_store_last_compacted_index_meta
                        .clone(),
                })
                .await?;
                continue;
            }
        }

        return Err(output_err);
    }
}

fn extract_file_already_exists_error_path(e: &util::anyhow::Error) -> Option<PathBuf> {
    if let Some(tantivy_error) = e.downcast_ref::<tantivy::TantivyError>() {
        if let tantivy::TantivyError::OpenWriteError(open_write_error) = tantivy_error {
            if let tantivy::directory::error::OpenWriteError::FileAlreadyExists(path) =
                open_write_error
            {
                if let Some(file_name) = path.file_name() {
                    if extract_opstamp(Path::new(file_name)).is_some() {
                        return Some(path.clone());
                    }
                }
            }
        }
    }
    None
}

async fn should_recompact_invalid_segment(
    segment_id: Uuid,
    index: &StoreInfo,
    last_compacted_tantivy_meta: Option<IndexMetaJson>,
    validate_opts: &ValidateTantivyIndexOptions,
) -> Result<bool> {
    let tantivy_meta = match last_compacted_tantivy_meta {
        Some(meta) => meta,
        None => {
            log::debug!("No last_compacted_tantivy_meta found for segment {}. Validation vacuously succeeded.", segment_id);
            return Ok(false);
        }
    };
    let validation_result = validate_tantivy_index(
        tantivy_meta,
        &index.store,
        &index.prefix,
        &TantivyIndexScope::Segment(segment_id),
        validate_opts,
    )
    .await?;
    match validation_result.check_success() {
        Ok(validated_metadata) => {
            log::debug!(
                "Index validation succeeded for segment {}. last_compacted_index_meta: {:?}",
                segment_id,
                serde_json::to_string(&validated_metadata)?
            );
            Ok(false)
        }
        Err(e) => {
            log::warn!(
                "Index validation failed for segment {}. {}. Recompacting and retrying.",
                segment_id,
                e
            );
            Ok(true)
        }
    }
}
