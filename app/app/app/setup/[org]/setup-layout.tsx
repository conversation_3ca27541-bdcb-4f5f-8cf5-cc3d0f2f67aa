"use client";

import { useUser } from "#/utils/user";

import { AccessFailed } from "#/ui/access-failed";
import Header from "#/ui/layout/header";
import { useIsClient } from "#/utils/use-is-client";
import { Spinner } from "#/ui/icons/spinner";
import { FeatureFlagProvider } from "#/lib/feature-flags-provider";

export function SetupLayout({
  children,
  orgName,
}: {
  children: React.ReactNode;
  orgName: string;
}) {
  const { orgs, status } = useUser();
  const org = orgs[orgName];

  const isClient = useIsClient();

  if (status === "loading" || !isClient) {
    return (
      <div className="size-screen flex items-center justify-center">
        <Spinner />
      </div>
    );
  }

  if (!org) {
    return (
      <div className="h-screen w-screen items-center justify-center">
        <Header noCTA />
        <AccessFailed objectType="organization" objectName={orgName} />
      </div>
    );
  }

  return (
    <div className="flex h-screen w-screen overflow-y-auto bg-primary-50">
      <div className="relative mx-auto flex size-full max-w-screen-sm px-4 pt-40">
        <FeatureFlagProvider>{children}</FeatureFlagProvider>
      </div>
    </div>
  );
}
