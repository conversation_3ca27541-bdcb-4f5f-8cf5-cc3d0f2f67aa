import {
  Field,
  Float64,
  Int32,
  Int64,
  List,
  Schema,
  Struct,
  TimestampMicrosecond,
  Utf8,
  Bool,
  type DataType,
} from "apache-arrow";
import { type SyncedPlaygroundBlock } from "#/ui/prompts/schema";
import { type ModelSpec } from "@braintrust/proxy/schema";
import {
  ComputedCostMetricFields,
  ComputedDurationMetricFields,
  ComputedTokenMetricFields,
  type ScoreSummary,
} from "@braintrust/local/query";
import { computeDisplayPaths } from "#/utils/display-paths";
import { createArrowTableFromRecords } from "#/utils/arrow";
import {
  GRID_EXPECTED_COLUMN_ID,
  GRID_METADATA_COLUMN_ID,
} from "#/ui/table/formatters/grid-layout-columns";
import { newId } from "braintrust";
import { makeFormatterMap } from "#/ui/table/formatters/header-formatters";
import { type FormatterMap } from "#/ui/field-to-column";
import { InputFormatter } from "#/ui/table/formatters/input-formatter";
import { StartEndFormatter } from "#/ui/table/formatters/start-end-formatter";
import { SpanTypeInfoFormatter } from "#/ui/table/formatters/span-info-formatter";
import { DurationWithAggregationFormatter } from "#/ui/table/formatters/duration-formatter";
import { DefaultWithAggregationFormatter } from "#/ui/table/formatters/default-formatter";
import { CostWithAggregationFormatter } from "#/ui/table/formatters/cost-formatter";
import { GROUP_BY_NONE_VALUE } from "#/ui/charts/selectionTypes";
import { isObject, getObjValueByPath } from "braintrust/util";

export type DatasetRow = {
  id: string;
  input: string | null;
  expected: unknown;
  metadata: Record<string, unknown> | null;
};

export const DATASETS = [
  {
    id: "dataset-1",
    name: "Dataset",
    project_id: "logged_out_playground",
    project_name: "",
  },
];

const GROUP_AGGREGATION_PROPS = {
  groupAggregationTypes: {
    scores: "avg" as const,
    metrics: "sum" as const,
  },
  setGroupAggregationType: () => {},
  isGrouping: false,
  summaryData: undefined,
  summaryEnabled: true,
};

const BASE_FORMATTERS: FormatterMap<{}, ""> = {
  input: {
    cell: InputFormatter,
    ignoreMultilineRendering: true,
  },
  metadata: {
    cell: InputFormatter,
  },
  start: {
    cell: StartEndFormatter,
  },
  end: {
    cell: StartEndFormatter,
  },
  span_type_info: {
    cell: SpanTypeInfoFormatter,
    ignoreMultilineRendering: true,
  },
  ...Object.fromEntries(
    ComputedDurationMetricFields.map((f) => [
      f,
      DurationWithAggregationFormatter(GROUP_AGGREGATION_PROPS),
    ]),
  ),
  ...Object.fromEntries(
    ComputedTokenMetricFields.map((f) => [
      f,
      DefaultWithAggregationFormatter(GROUP_AGGREGATION_PROPS),
    ]),
  ),
  ...Object.fromEntries(
    ComputedCostMetricFields.map((f) => [
      f,
      CostWithAggregationFormatter(GROUP_AGGREGATION_PROPS),
    ]),
  ),
};

export const FORMATTERS = makeFormatterMap(BASE_FORMATTERS);

export const NEVER_VISIBLE_COLUMNS = new Set([
  "comparison_key",
  "__bt_internal",
]);
export const LIST_INITIALLY_VISIBLE_COLUMNS = { id: false };
export const GRID_INITIALLY_VISIBLE_COLUMNS = {
  [GRID_EXPECTED_COLUMN_ID]: false,
  [GRID_METADATA_COLUMN_ID]: false,
};

export const GRID_PROJECTED_PATHS = [
  "comparison_key",
  "input",
  "output",
  "expected",
  "metadata",
  "id",
];

export const TYPE_HINTS = {
  input: "JSON",
  output: "JSON",
  expected: "JSON",
  metadata: "JSON",
  span_attributes: "JSON",
  metrics: "JSON",
};

export const STREAMING_CONTENT_PROPS = {
  generationIdToPromptId: {},
  havePlaygroundLogsLoaded: false,
  runPrompts: () => {},
};

const METRIC_NAMES = [
  "duration",
  "llm_duration",
  "prompt_tokens",
  "completion_tokens",
  "total_tokens",
  "prompt_cached_tokens",
  "prompt_cache_creation_tokens",
  "estimated_cost",
  "start",
  "end",
] as const;

const DEFAULT_TIMESTAMP = Date.now();
const DEFAULT_GENERATION_ID = newId();
const DEFAULT_XACT_ID = "0";

// Helper function to create a metric score object
export const createMetricScore = (
  metricName: string,
  isComparisonMetric = false,
): ScoreSummary[string] => {
  const baseScore = {
    avg: null,
    min: null,
    max: null,
    sum: null,
    stddev: null,
    all_keys: null,
  };

  if (isComparisonMetric) {
    return {
      ...baseScore,
      _name: metricName,
      diffAvg: null,
      diffMin: null,
      diffMax: null,
      diffSum: null,
      compareAvg: null,
      compareMin: null,
      compareMax: null,
      compareSum: null,
      improvements: null,
      regressions: null,
      equal: null,
      compare_keys: null,
      compare_all_avg: null,
      compare_all_min: null,
      compare_all_max: null,
      compare_all_sum: null,
      compare_all_keys: null,
    };
  }

  return baseScore;
};

// Helper function to create scores object
export const createScores = (): ScoreSummary => {
  const scores: ScoreSummary = {};

  METRIC_NAMES.forEach((metricName) => {
    // Use the derived comparison metrics list instead of hardcoded checks
    const isComparisonMetric =
      metricName === "duration" ||
      metricName === "llm_duration" ||
      metricName === "prompt_tokens" ||
      metricName === "completion_tokens" ||
      metricName === "total_tokens" ||
      metricName === "estimated_cost" ||
      metricName === "prompt_cached_tokens" ||
      metricName === "prompt_cache_creation_tokens";
    scores[metricName] = createMetricScore(metricName, isComparisonMetric);
  });

  return scores;
};

// Function to generate summary breakdown data
export const generateSummaryBreakdownData = (
  promptBlocks: SyncedPlaygroundBlock[],
  allAvailableModels: Record<string, ModelSpec>,
) => {
  const experiments = promptBlocks.map((block, index) => {
    const modelId = block.prompt_data?.options?.model;
    const model = modelId ? allAvailableModels[modelId] : undefined;
    const experimentName = model?.displayName || modelId || `Task ${index + 1}`;

    const experiment = {
      id: `experiment-${index + 1}-${Date.now()}`,
      name: experimentName,
      index,
      type: index === 0 ? ("base" as const) : ("comparison" as const),
    };

    return {
      scores: createScores(),
      groupedSummary: {},
      groupTotals: {},
      experiment,
    };
  });

  return {
    summary: {
      experiments,
    },
    hasGroups: false,
    aggregationTypes: {
      scores: "avg" as const,
      metrics: "sum" as const,
    },
  };
};

// Schema creation functions
export const createDiffStruct = (
  baseType: DataType,
  promptBlocks: SyncedPlaygroundBlock[],
) => {
  const children = [
    Field.new({
      name: "_bt_internal_right",
      type: baseType,
      nullable: true,
    }), // e1
  ];

  if (promptBlocks.length > 1) {
    children.push(
      Field.new({
        name: "_bt_internal_left",
        type: baseType,
        nullable: true,
      }), // e2
    );
  }

  // Add e3, e4, etc. for additional experiments
  promptBlocks.slice(2).forEach((_, index) => {
    const experimentKey = `e${index + 3}`; // e3, e4, e5, etc.
    children.push(
      Field.new({ name: experimentKey, type: baseType, nullable: true }),
    );
  });

  return new Struct(children);
};

export const createMetricsStruct = (promptBlocks: SyncedPlaygroundBlock[]) => {
  const metricChildren = METRIC_NAMES.map((metricName) =>
    Field.new({
      name: metricName,
      type: createDiffStruct(new Float64(), promptBlocks),
      nullable: true,
    }),
  );

  return new Struct(metricChildren);
};

export const createBtInternalFields = (
  promptBlocks: SyncedPlaygroundBlock[],
  isListLayout: boolean,
) => {
  const btInternalFields = [
    Field.new({
      name: "_meta",
      type: new Struct([
        Field.new({
          name: "diffModeEnabled",
          type: isListLayout ? new Bool() : new Utf8(),
          nullable: true,
        }),
        Field.new({ name: "layoutType", type: new Utf8(), nullable: true }),
      ]),
      nullable: true,
    }),
    Field.new({
      name: "e1",
      type: new Struct([
        Field.new({
          name: "rowCount",
          type: isListLayout ? new Int64() : new Utf8(),
          nullable: true,
        }),
      ]),
      nullable: true,
    }),
  ];

  // Add e2, e3, etc. for each prompt block
  promptBlocks.slice(1).forEach((_, index) => {
    const experimentKey = `e${index + 2}`;
    btInternalFields.push(
      Field.new({
        name: experimentKey,
        type: new Struct([
          Field.new({
            name: "compareIds",
            type: isListLayout
              ? new List(
                  Field.new({ name: "l", type: new Int32(), nullable: true }),
                )
              : new Utf8(),
            nullable: true,
          }),
          ...(isListLayout
            ? []
            : [Field.new({ name: "data", type: new Utf8(), nullable: true })]),
        ]),
        nullable: true,
      }),
    );
  });

  return btInternalFields;
};

export const createSchema = (
  promptBlocks: SyncedPlaygroundBlock[],
  layout: string | null,
) => {
  const isListLayout = layout === "list";

  if (isListLayout) {
    const fields = [
      Field.new({
        name: "comparison_key",
        type: createDiffStruct(new Utf8(), promptBlocks),
        nullable: true,
      }),
      Field.new({
        name: "span_type_info",
        type: createDiffStruct(new Utf8(), promptBlocks),
        nullable: true,
      }),
      Field.new({
        name: "input",
        type: createDiffStruct(new Utf8(), promptBlocks),
        nullable: true,
      }),
      Field.new({
        name: "output",
        type: createDiffStruct(new Utf8(), promptBlocks),
        nullable: true,
      }),
      Field.new({
        name: "expected",
        type: createDiffStruct(new Utf8(), promptBlocks),
        nullable: true,
      }),
      Field.new({
        name: "metrics",
        type: createMetricsStruct(promptBlocks),
        nullable: true,
      }),
      Field.new({
        name: "metadata",
        type: createDiffStruct(new Utf8(), promptBlocks),
        nullable: true,
      }),
      Field.new({
        name: "id",
        type: createDiffStruct(new Utf8(), promptBlocks),
        nullable: true,
      }),
      Field.new({
        name: "created",
        type: createDiffStruct(new TimestampMicrosecond(), promptBlocks),
        nullable: true,
      }),
      Field.new({
        name: "__bt_group_key",
        type: new Utf8(),
        nullable: true,
      }),
    ];

    // Add __bt_internal for list layout
    const btInternalChildren = createBtInternalFields(promptBlocks, true);
    fields.push(
      Field.new({
        name: "__bt_internal",
        type: new Struct(btInternalChildren),
        nullable: true,
      }),
    );

    return new Schema(fields);
  } else {
    // Grid layout: simple flat structure
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const fields: Field<any>[] = [
      Field.new({ name: "comparison_key", type: new Utf8(), nullable: true }),
      Field.new({ name: "input", type: new Utf8(), nullable: true }),
      Field.new({ name: "output", type: new Utf8(), nullable: true }),
      Field.new({ name: "expected", type: new Utf8(), nullable: true }),
      Field.new({ name: "metadata", type: new Utf8(), nullable: true }),
      Field.new({ name: "id", type: new Utf8() }),
      Field.new({ name: "__bt_group_key", type: new Utf8(), nullable: true }),
    ];

    // Create dynamic __bt_internal structure for grid layout
    const btInternalFields = createBtInternalFields(promptBlocks, false);
    fields.push(
      Field.new({
        name: "__bt_internal",
        type: new Struct(btInternalFields),
        nullable: true,
      }),
    );

    return new Schema(fields);
  }
};

// Display paths creation
export const createDisplayPaths = (layout: string | null, schema: Schema) => {
  if (layout === "list") {
    // List layout: create displayPaths directly like real playground
    return {
      type: "node" as const,
      children: {
        comparison_key: { type: "leaf" as const },
        span_type_info: { type: "leaf" as const },
        input: { type: "leaf" as const },
        output: { type: "leaf" as const },
        expected: { type: "leaf" as const },
        metrics: {
          type: "node" as const,
          children: Object.fromEntries(
            METRIC_NAMES.map((metricName) => [
              metricName,
              { type: "leaf" as const },
            ]),
          ),
        },
        metadata: { type: "leaf" as const },
        id: { type: "leaf" as const },
        created: { type: "leaf" as const },
      },
    };
  } else {
    // Grid layout: use computeDisplayPaths for simple case
    const projectedPaths = GRID_PROJECTED_PATHS;
    return computeDisplayPaths(schema, projectedPaths);
  }
};

const createDiffValue = (
  baseValue: unknown,
  promptBlocks: SyncedPlaygroundBlock[],
) => {
  const diffStruct: Record<string, unknown> = {
    _bt_internal_right: baseValue, // e1
    _bt_internal_left: baseValue, // e2 (first comparison)
  };

  // Add e3, e4, etc. for additional experiments
  promptBlocks.slice(2).forEach((_, index) => {
    const experimentKey = `e${index + 3}`; // e3, e4, e5, etc.
    diffStruct[experimentKey] = baseValue;
  });

  return diffStruct;
};

const createMetricsValue = (promptBlocks: SyncedPlaygroundBlock[]) => {
  const metrics: Record<string, unknown> = {};
  METRIC_NAMES.forEach((metricName) => {
    metrics[metricName] = createDiffValue(null, promptBlocks);
  });

  return metrics;
};

const createBtInternalData = (
  promptBlocks: SyncedPlaygroundBlock[],
  datasetRows: DatasetRow[],
  layout: string | null,
) => {
  const btInternalData: Record<string, unknown> = {
    _meta: {
      diffModeEnabled: false,
      layoutType: layout,
    },
    e1: {
      rowCount: datasetRows.length,
    },
  };

  // Add e2, e3, etc. for each prompt block
  promptBlocks.slice(1).forEach((_, index) => {
    const experimentKey = `e${index + 2}`;
    btInternalData[experimentKey] =
      layout === "list" ? { compareIds: [] } : { compareIds: [], data: {} };
  });

  return btInternalData;
};

const getGroupKey = (
  row: DatasetRow,
  tableGrouping: string | undefined,
): string => {
  if (tableGrouping && tableGrouping !== GROUP_BY_NONE_VALUE && row.metadata) {
    try {
      const metadataPath = JSON.parse(tableGrouping);
      const metadataValue = getMetadataValue(row.metadata, metadataPath);
      return isObject(metadataValue)
        ? JSON.stringify(metadataValue)
        : String(metadataValue);
    } catch {
      return String(null);
    }
  }
  return String(null);
};

export const createMockData = (
  promptBlocks: SyncedPlaygroundBlock[],
  layout: string | null,
  schema: Schema,
  datasetRows: DatasetRow[],
  tableGrouping?: string,
) => {
  const isListLayout = layout === "list";
  const btInternalData = createBtInternalData(
    promptBlocks,
    datasetRows,
    layout,
  );

  if (isListLayout) {
    const baseSpanInfo = {
      name: "eval",
      type: "eval",
      cached: 0,
      remote: 0,
      has_error: false,
      in_progress: false,
      generation: DEFAULT_GENERATION_ID,
    };

    const sampleRows = datasetRows.map((row, index) => ({
      comparison_key: createDiffValue(`row-${index + 1}-key`, promptBlocks),
      span_type_info: createDiffValue(
        JSON.stringify(baseSpanInfo),
        promptBlocks,
      ),
      input: createDiffValue(JSON.stringify(row.input), promptBlocks),
      output: createDiffValue(null, promptBlocks),
      expected: createDiffValue(row.expected, promptBlocks),
      scores: createDiffValue(null, promptBlocks),
      metrics: createMetricsValue(promptBlocks),
      metadata: createDiffValue(row.metadata, promptBlocks),
      id: createDiffValue(row.id, promptBlocks),
      created: createDiffValue(DEFAULT_TIMESTAMP, promptBlocks),
      __bt_group_key: getGroupKey(row, tableGrouping),
      __bt_internal: btInternalData,
    }));

    const table = createArrowTableFromRecords(sampleRows, schema);
    const result = table?.toArray() ?? [];
    return result;
  } else {
    const sampleRows = datasetRows.map((row) => ({
      comparison_key: `row-${row.id}-key`,
      _xact_id: DEFAULT_XACT_ID,
      input: row.input,
      output: null,
      expected: row.expected,
      metadata: row.metadata,
      id: row.id,
      __bt_group_key: getGroupKey(row, tableGrouping),
      __bt_internal: btInternalData,
    }));

    const table = createArrowTableFromRecords(sampleRows, schema);
    return table?.toArray() ?? [];
  }
};

export const createExperimentName = (
  promptBlocks: SyncedPlaygroundBlock[],
  allAvailableModels: Record<string, ModelSpec>,
) => {
  if (promptBlocks.length === 0) return "Playground";
  const firstBlock = promptBlocks[0];
  const modelId = firstBlock.prompt_data?.options?.model;
  const model = modelId ? allAvailableModels[modelId] : undefined;
  return model?.displayName || modelId || "Task 1";
};

export const createComparisonExperiments = (
  promptBlocks: SyncedPlaygroundBlock[],
  allAvailableModels: Record<string, ModelSpec>,
) => {
  // Skip the first prompt block (that becomes the main experiment)
  return promptBlocks.slice(1).map((block, index) => {
    const modelId = block.prompt_data?.options?.model;
    const model = modelId ? allAvailableModels[modelId] : undefined;
    return {
      id: `e${index + 2}`, // e2, e3, etc. - must match __bt_internal keys
      name: model?.displayName || modelId || `Task ${index + 2}`,
      object_type: "experiment" as const,
    };
  });
};

export const createMultilineRow = (
  layout: string | null,
  rowHeight: string | undefined,
  promptBlocksLength: number,
) => {
  const isListLayout = layout === "list";
  const showMultiExperimentRowHeight = isListLayout && promptBlocksLength > 1;

  // For list layout with multiple experiments, use experiment count
  // For grid layout or single experiment, use the row height setting
  let tableRowHeightCount: number;

  if (showMultiExperimentRowHeight) {
    tableRowHeightCount = promptBlocksLength;
  } else if (rowHeight === "tall") {
    tableRowHeightCount = 6;
  } else if (rowHeight === "compact") {
    tableRowHeightCount = 1;
  } else {
    // Default to normal height
    tableRowHeightCount = 2;
  }

  return {
    numRows: tableRowHeightCount,
    gapSize: showMultiExperimentRowHeight ? 6 : 0,
    numGroupRows: undefined,
    isGridLayout: layout === "grid",
  };
};

export const extractMetadataPaths = (datasetRows: DatasetRow[]) => {
  const leafPaths = new Set<string>();

  datasetRows.forEach((row) => {
    if (
      row.metadata &&
      typeof row.metadata === "object" &&
      !Array.isArray(row.metadata)
    ) {
      const extractPaths = (obj: object, currentPath: string[] = []) => {
        Object.entries(obj).forEach(([key, value]) => {
          const newPath = [...currentPath, key];
          const pathString = newPath.join(".");

          // Only add leaf paths (paths that don't have nested objects)
          if (
            value === null ||
            value === undefined ||
            typeof value !== "object" ||
            Array.isArray(value)
          ) {
            leafPaths.add(pathString);
          }

          if (value && typeof value === "object" && !Array.isArray(value)) {
            extractPaths(value, newPath);
          }
        });
      };

      extractPaths(row.metadata);
    }
  });

  const pathArray = Array.from(leafPaths).sort();
  const result: string[] = [];

  for (const path of pathArray) {
    const hasShallowerPath = pathArray.some(
      (otherPath) => otherPath !== path && path.startsWith(otherPath + "."),
    );

    if (!hasShallowerPath) {
      result.push(path);
    }
  }

  return result;
};

export const getMetadataValue = (
  metadata: Record<string, unknown> | null,
  path: string[],
): unknown => {
  if (!metadata) return null;
  return getObjValueByPath(metadata, path);
};
