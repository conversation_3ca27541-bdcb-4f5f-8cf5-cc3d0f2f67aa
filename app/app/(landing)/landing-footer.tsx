"use client";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "#/ui/landing/logo";
import { StructuredData } from "#/ui/structured-data";
import { cn } from "#/utils/classnames";
import { ParticleFlux } from "./particle-flux";

const footerConfig = [
  {
    title: "Resources",
    links: [
      {
        title: "Documentation",
        href: "/docs",
        description: "Complete guide to Braintrust AI evaluation platform",
      },
      {
        title: "Eval via UI",
        href: "/docs/start/eval-ui",
        description: "Get started with evaluations using our web interface",
      },
      {
        title: "Eval via SDK",
        href: "/docs/start/eval-sdk",
        description: "Integrate evaluations programmatically with our SDK",
      },
      {
        title: "Guides",
        href: "/docs/guides",
        description: "Step-by-step guides for advanced features",
      },
      {
        title: "Cookbook",
        href: "/docs/cookbook",
        description: "Code examples and practical recipes",
      },
      {
        title: "Changelog",
        href: "/docs/changelog",
        description: "Latest updates and feature releases",
      },
      {
        title: "Articles",
        href: "/articles",
        description: "In-depth articles and insights",
      },
    ],
  },
  {
    title: "Company",
    links: [
      {
        title: "Pricing",
        href: "/pricing",
        description: "Flexible pricing plans for teams of all sizes",
      },
      {
        title: "Blog",
        href: "/blog",
        description: "Latest insights on AI evaluation and LLM best practices",
      },
      {
        title: "Careers",
        href: "/careers",
        description: "Join our team building the future of AI evaluation",
      },
      {
        title: "Contact us",
        href: "/contact",
        description: "Get in touch with our team",
      },
      {
        title: "Terms of Service",
        href: "/legal/terms-of-service",
        description: "Legal terms and conditions",
      },
      {
        title: "Privacy Policy",
        href: "/legal/privacy-policy",
        description: "How we protect your data",
      },
    ],
  },
  {
    title: "Community",
    links: [
      {
        title: "GitHub",
        href: "https://github.com/braintrustdata/",
        description: "Open source libraries and tools",
      },
      {
        title: "Discord",
        href: "https://discord.gg/6G8s47F44X",
        description: "Join our developer community",
      },
      {
        title: "X",
        href: "https://twitter.com/braintrustdata",
        description: "Latest news and updates",
      },
      {
        title: "YouTube",
        href: "https://www.youtube.com/@BraintrustData",
        description: "Video tutorials and demos",
      },
      {
        title: "LinkedIn",
        href: "https://www.linkedin.com/company/braintrust-data",
        description: "Follow us for company updates",
      },
    ],
  },
];

const organizationSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  name: "Braintrust",
  url: "https://braintrust.dev",
  logo: "https://braintrust.dev/logo.png",
  description:
    "The enterprise-grade AI evaluation platform for building reliable LLM applications",
  foundingDate: "2023",
  industry: "Software Development",
  hasOfferCatalog: {
    "@type": "OfferCatalog",
    name: "AI Evaluation Services",
    itemListElement: [
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "AI Model Evaluation",
          description: "Comprehensive evaluation tools for LLM applications",
        },
      },
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "Dataset Management",
          description:
            "Scalable dataset storage and versioning for AI evaluations",
        },
      },
      {
        "@type": "Offer",
        itemOffered: {
          "@type": "Service",
          name: "Prompt Engineering",
          description:
            "Interactive playgrounds for prompt development and optimization",
        },
      },
    ],
  },
  contactPoint: {
    "@type": "ContactPoint",
    contactType: "customer service",
    url: "https://braintrust.dev/contact",
  },
  sameAs: [
    "https://github.com/braintrustdata/",
    "https://discord.gg/6G8s47F44X",
    "https://www.linkedin.com/company/braintrust-data",
    "https://www.youtube.com/@BraintrustData",
    "https://twitter.com/braintrustdata",
  ],
};

const websiteSchema = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  name: "Braintrust",
  url: "https://braintrust.dev",
  description:
    "The enterprise-grade AI evaluation platform for building reliable LLM applications",
  potentialAction: {
    "@type": "SearchAction",
    target: "https://braintrust.dev/docs?q={search_term_string}",
    "query-input": "required name=search_term_string",
  },
};

export const LandingFooter = () => {
  return (
    <div className="z-10 overflow-hidden px-4 py-8 font-display bg-black text-white sm:px-8">
      <StructuredData data={organizationSchema} />
      <StructuredData data={websiteSchema} />
      <div className="mx-auto w-full max-w-landing bg-transparent">
        <footer className="z-20 flex" role="contentinfo">
          <nav className="max-w-screen-landing w-full">
            <div className="grid-rows-auto grid grid-cols-3 gap-8 md:grid-cols-4">
              <div className="col-span-3 flex flex-col justify-between md:col-span-1">
                <Icon size={48} />
                <div
                  className="hidden flex-col pt-8 text-white md:block md:pt-16 "
                  aria-labelledby="brand-section"
                >
                  <Logo width="100%" />
                </div>
              </div>
              {footerConfig.map((section, idx) => (
                <div
                  className={cn(
                    "flex flex-1 md:border-l md:border-t-0 md:col-span-1 pt-8 md:pt-0 md:px-4 pb-8 border-white md:pb-44 col-span-3 border-t",
                    {
                      "flex-col justify-between md:pb-0":
                        idx === footerConfig.length - 1,
                    },
                  )}
                  key={idx}
                  aria-labelledby={`footer-${section.title.toLowerCase()}`}
                >
                  <ul className="group flex h-fit flex-col gap-2">
                    <li
                      className="font-suisse text-[10px] uppercase tracking-widest"
                      id={`footer-${section.title.toLowerCase()}`}
                    >
                      {section.title}
                    </li>
                    {section.links.map((link, idx) => (
                      <li key={idx}>
                        <a
                          aria-describedby={
                            link.description ? `${link.href}-desc` : undefined
                          }
                          title={link.description}
                          href={link.href}
                          {...(link.href.startsWith("http")
                            ? { target: "_blank", rel: "noopener noreferrer" }
                            : {})}
                          className="block text-lg transition-opacity text-white hover:!opacity-100 group-hover:opacity-60"
                        >
                          {link.title}
                        </a>
                        {link.description && (
                          <span id={`${link.href}-desc`} className="sr-only">
                            {link.description}
                          </span>
                        )}
                      </li>
                    ))}
                  </ul>
                  {idx === footerConfig.length - 1 && (
                    <p className="mt-28 font-suisse text-[10px] uppercase tracking-wide md:mt-0">
                      Copyright ©2025 Braintrust Data, Inc.
                    </p>
                  )}
                </div>
              ))}
              <div
                className="col-span-3 block translate-y-10 md:hidden"
                aria-labelledby="brand-section"
              >
                <Logo width="100%" />
              </div>
            </div>
          </nav>
        </footer>
      </div>
    </div>
  );
};

const EMPTY_ARRAY: string[] = [];

export const LandingFooterFlux = ({
  bgColor,
  color = "#000000",
  className,
  highlightColors = EMPTY_ARRAY,
  rows = 1,
}: {
  bgColor?: string;
  color?: string;
  className?: string;
  rows?: number;
  highlightColors?: string[];
}) => (
  <div className={cn("relative h-36 w-full", className)}>
    <ParticleFlux
      highlightColors={highlightColors}
      color={color}
      bgColor={bgColor}
      className="z-10"
      rows={rows}
      cols={24}
      rightBuffer={0}
      waveAmplitude={50}
    />
  </div>
);
