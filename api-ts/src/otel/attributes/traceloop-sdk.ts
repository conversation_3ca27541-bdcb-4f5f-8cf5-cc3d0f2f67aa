import { z } from "zod";
import { get } from "lodash";
import { isEmpty } from "braintrust/util";
import { ChatCompletionTool } from "@braintrust/typespecs";
import {
  SpanSpec,
  translateModelParams,
  notHandled,
  handled,
} from "./attributes";

export const traceloopSpanSpec: SpanSpec = {
  input: (attributes) => {
    const input = get(attributes, "traceloop.entity.input");
    if (typeof input !== "string") {
      throw new Error("Expected traceloop.entity.input to be a string");
    }
    return handled({ isLLM: false, data: JSON.parse(input) });
  },
  output: (attributes) => {
    const output = get(attributes, "traceloop.entity.output");
    if (typeof output !== "string") {
      throw new Error("Expected traceloop.entity.output to be a string");
    }
    return handled({ isLLM: false, data: JSON.parse(output) });
  },
  metadata: (attributes) => {
    const baseParams = translateModelParams(get(attributes, "llm.request"));
    const tools = extractTools(attributes);
    const result = {
      ...baseParams,
      ...(tools
        ? {
            tools,
            functions: undefined,
          }
        : {}),
    };

    // Filter out undefined values
    const filteredResult = Object.fromEntries(
      Object.entries(result).filter(([_, value]) => value !== undefined),
    );

    if (Object.keys(filteredResult).length === 0) {
      return notHandled;
    }

    return handled(filteredResult);
  },
  metrics: (attributes) => {
    const tokens = get(attributes, "llm.usage.total_tokens");
    if (tokens === undefined || tokens === null) {
      return notHandled;
    }

    return handled({
      tokens,
    });
  },
};

const traceloopToolsSchema = z.array(
  z.object({
    name: z.string().default(""),
    description: z.string().optional(),
    parameters: z
      .string()
      .optional()
      .transform((value) => {
        if (!value) {
          return undefined;
        }
        return JSON.parse(value);
      }),
  }),
);

function extractTools(attributes: unknown): ChatCompletionTool[] | undefined {
  for (const path of ["llm.request.tools", "llm.request.functions"]) {
    const value = get(attributes, path);
    if (!isEmpty(value)) {
      const functions = traceloopToolsSchema.parse(value);
      return functions.map((f) => ({
        type: "function",
        function: f,
      }));
    }
  }
}
