"use client";
import { <PERSON><PERSON>, DialogContent } from "#/ui/dialog";
import { useContext, useCallback } from "react";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useOrg } from "#/utils/user";
import { OnlineScoreConfigForm } from "../configuration/online-scoring/online-score-config-form";
import { Button } from "#/ui/button";
import { type ProjectScore } from "@braintrust/typespecs";
import { performDelete } from "../configuration/configuration-client-actions";
import { useSessionToken } from "#/utils/auth/session-token";
import { toast } from "sonner";

//TODO: move this file into some shared file, as it's used for both logs and configuration
interface ConfigureOnlineScoringRuleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedRule?: ProjectScore | null;
  showDelete?: boolean;
}

export function ConfigureOnlineScoringRuleDialog({
  open,
  onOpenChange,
  selectedRule,
  showDelete = false,
}: ConfigureOnlineScoringRuleDialogProps) {
  const { name: orgName } = useOrg();
  const { projectId, mutateConfig } = useContext(ProjectContext);
  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  const handleDeleteRule = useCallback(
    async (rule: ProjectScore) => {
      if (!apiUrl) {
        toast.error("Not logged in");
        return;
      }
      return toast.promise(
        performDelete({
          apiUrl,
          sessionToken: await getOrRefreshToken(),
          objectType: "project_score",
          rowId: rule.id,
          mutate: mutateConfig,
        }),
        {
          loading: "Deleting online scoring rule",
          success: () => {
            onOpenChange(false);
            return `Successfully deleted "${rule.name}"`;
          },
          error: "Failed to delete online scoring rule",
        },
      );
    },
    [apiUrl, getOrRefreshToken, mutateConfig, onOpenChange],
  );

  if (!projectId) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex flex-col overflow-hidden p-0 sm:max-w-2xl">
        <OnlineScoreConfigForm
          orgName={orgName}
          row={selectedRule ?? null} // null for new rule, existing rule for editing
          onClose={() => onOpenChange(false)}
          leftFooterSlot={
            <div className="flex items-center gap-2">
              {showDelete && selectedRule && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-bad-700 hover:bg-bad-50 hover:text-bad-800"
                  onClick={(e: React.MouseEvent) => {
                    e.preventDefault();
                    handleDeleteRule(selectedRule);
                  }}
                >
                  Delete rule
                </Button>
              )}
            </div>
          }
        />
      </DialogContent>
    </Dialog>
  );
}
