import { useMemo, useState } from "react";

import {
  type HighlightState,
  type HighlightedGroup,
} from "#/ui/charts/highlight";
import { type SelectionType } from "#/ui/charts/selectionTypes";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import { type TimeseriesAggregates } from "#/ui/charts/timeseries-data/chart-data.types";
import { type TimeseriesVizType } from "#/ui/charts/timeseries/timeseries.types";

import { LEGEND_ENTRY_HEIGHT } from "./timeseries-legend.constants";
import { TimeseriesLegendLabels } from "./timeseries-legend-labels";
import type { TimeseriesLegendLabelData } from "./timeseries-legend.types";

export interface MonitorChartLegendProps {
  highlightState: HighlightState;
  addSelected: (v: HighlightedGroup) => void;
  removeSelected: (v: HighlightedGroup) => void;
  setSelected: (v: HighlightedGroup) => void;
  clearSelected: () => void;
  highlight: (v: HighlightedGroup) => void;
  clearHighlight: () => void;
  seriesAggregates: TimeseriesAggregates[];
  seriesMetadata: { selectionType: SelectionType; name: string }[];
  groupBys?: string[];
  aggregateType: "sum" | "average";
  aggregateFormatter: (value: number) => string;
  height: number;
  width: number;
  vizType?: TimeseriesVizType;
  isStackedTotal?: boolean;
  nearestIndex?: number;
}

const MonitorChartLegend = ({
  highlightState,
  seriesAggregates,
  seriesMetadata,
  addSelected,
  removeSelected,
  clearSelected,
  setSelected,
  highlight,
  clearHighlight,
  groupBys,
  aggregateType,
  aggregateFormatter,
  height,
  width,
  vizType = "lines",
  isStackedTotal,
  nearestIndex,
}: MonitorChartLegendProps) => {
  const [isLegendHovered, setIsLegendHovered] = useState<boolean>(false);

  const legendLabelsData: TimeseriesLegendLabelData[] = useMemo(() => {
    return seriesMetadata.map((data, i) => {
      const { sum, count } = seriesAggregates[i];
      const common = {
        groupVal: data.selectionType,
        type: "points" as const,
      };
      if (aggregateType === "sum") {
        return {
          label: data.name,
          colorIndex: i,
          formattedValue: aggregateFormatter(sum),
          ...common,
        };
      }

      if (count === 0) {
        return {
          label: data.name,
          colorIndex: i,
          formattedValue: "0",
          ...common,
        };
      }
      const average = sum / count;

      return {
        label: data.name,
        colorIndex: i,
        formattedValue: aggregateFormatter(average),
        ...common,
      };
    });
  }, [seriesMetadata, seriesAggregates, aggregateType, aggregateFormatter]);

  const totalSum = useMemo(() => {
    return seriesAggregates.reduce((acc, aggr) => acc + aggr.sum, 0);
  }, [seriesAggregates]);

  const itemsOffset =
    legendLabelsData.length > 1 && vizType === "bars" ? -1 : 0; // bars needs row on top for total
  const legendEntriesAvailable =
    Math.floor(height / LEGEND_ENTRY_HEIGHT) + itemsOffset;

  // default we have enough space to show all
  const numLegendLabels = legendLabelsData.length;
  const showShowNumMore = legendEntriesAvailable <= numLegendLabels - 1;

  // remember we need to cut off an extra row for 'show more' row
  const numMore = showShowNumMore
    ? Math.min(numLegendLabels, numLegendLabels - legendEntriesAvailable + 1)
    : 0;

  return (
    <Tooltip open={numMore > 0 ? undefined : false} delayDuration={50}>
      <TooltipTrigger asChild>
        <div
          className={cn("flex-none w-full")}
          style={{ height: `${height}px` }}
          onMouseEnter={() => setIsLegendHovered(true)}
          onMouseLeave={() => setIsLegendHovered(false)}
        >
          <TimeseriesLegendLabels
            legendLabelData={legendLabelsData.slice(
              0,
              numLegendLabels - numMore,
            )}
            groupBys={groupBys}
            highlightState={highlightState}
            addSelected={addSelected}
            removeSelected={removeSelected}
            setSelected={setSelected}
            highlight={highlight}
            clearHighlight={clearHighlight}
            clearSelected={clearSelected}
            isLegendHovered={isLegendHovered}
            vizType={vizType}
            totalSumString={aggregateFormatter(totalSum)}
            isStackedTotal={isStackedTotal}
            nearestIndex={nearestIndex}
            isTooltip={false}
          />
          {showShowNumMore && (
            <div className="pt-2 text-xs text-primary-500">{numMore} more</div>
          )}
        </div>
      </TooltipTrigger>
      <TooltipContent
        className="rounded-md pr-0 pt-0 shadow-md duration-0 bg-primary-50 data-[side=bottom]:duration-0"
        style={{ width: `${width + 22 + 4}px` }}
        side="bottom"
        align="end"
        alignOffset={-10 - 4 + 1}
        sideOffset={-height - 1}
        avoidCollisions={false}
        onMouseEnter={() => setIsLegendHovered(true)}
        onMouseLeave={() => setIsLegendHovered(false)}
      >
        <TimeseriesLegendLabels
          className="pr-3"
          legendLabelData={legendLabelsData}
          groupBys={groupBys}
          highlightState={highlightState}
          addSelected={addSelected}
          removeSelected={removeSelected}
          setSelected={setSelected}
          clearSelected={clearSelected}
          highlight={highlight}
          clearHighlight={clearHighlight}
          isLegendHovered={isLegendHovered}
          vizType={vizType}
          totalSumString={aggregateFormatter(totalSum)}
          isStackedTotal={isStackedTotal}
          nearestIndex={nearestIndex}
          isTooltip={true}
        />
      </TooltipContent>
    </Tooltip>
  );
};

MonitorChartLegend.displayName = "MonitorChartLegend";
export { MonitorChartLegend };
