"use client";
import { type DialogProps } from "@radix-ui/react-dialog";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "#/ui/dialog";
import { type TransactionId } from "braintrust/util";
import { But<PERSON>, buttonVariants } from "#/ui/button";
import { Settings2, X } from "lucide-react";
import React, {
  type ReactNode,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { cn } from "#/utils/classnames";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useHotkeys, useHotkeysContext } from "react-hotkeys-hook";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import { Switch } from "#/ui/switch";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { type Roster } from "#/utils/realtime-data";
import { RosterAvatars } from "#/ui/roster";
import { type ConfiguredScore } from "./graph";
import { usePanelSize } from "#/ui/use-panel-size";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "#/ui/resizable";
import { Input } from "#/ui/input";
import { HotkeyScope } from "#/ui/hotkeys";
import { useHumanReviewState } from "#/ui/query-parameters";
import { DefaultBreadcrumbSection } from "#/ui/layout/breadcrumbs";
import { ExpandedHumanReviewForm } from "./expanded-human-review-form";
import { type ManualScoreUpdate } from "./trace";
import { type SavingState, SavingStatus } from "#/ui/saving";
import { isKeyboardEventTriggeredByInput } from "#/ui/hotkey-utils";
import { type ExpandedRowState } from "./query";

export const ExpandedHumanReviewModal = memo(
  ({
    children,
    scores,
    rowKey,
    rowIndex,
    totalRows,
    xactId,
    onPrevRow,
    onNextRow,
    onJumpToRow,
    updateScores,
    actions,
    currentTraceViewers,
    isDatasetRow,
    savingState,
    expandedRowState,
    ...rest
  }: React.PropsWithChildren<
    {
      scores: Record<string, ConfiguredScore>;
      rowKey?: string;
      rowIndex?: number;
      totalRows?: number;
      xactId: TransactionId | null;
      updateScores?: (
        scoreUpdates: ManualScoreUpdate[],
      ) => Promise<(TransactionId | null)[]>;
      onPrevRow?: (opts?: { withTransition?: boolean }) => Promise<unknown>;
      onNextRow?: (opts?: { withTransition?: boolean }) => Promise<unknown>;
      onJumpToRow?: (rowIndex: number) => void;
      actions: ReactNode;
      currentTraceViewers: Roster | null;
      isDatasetRow?: boolean;
      isSpanChangeTransitioning?: boolean;
      savingState?: SavingState;
      isLoading?: boolean;
      expandedRowState: ExpandedRowState;
    } & DialogProps
  >) => {
    const params = useParams<{ org: string; project: string }>();

    const [_, setHumanReviewState] = useHumanReviewState();

    const scoresArray = useMemo(
      () =>
        Object.values(scores).sort((a, b) => {
          if (!a.config.position || !b.config.position) return 0;
          return a.config.position.localeCompare(b.config.position);
        }),
      [scores],
    );

    const prevRow = useCallback(async () => {
      if (expandedRowState !== "fully_loaded") {
        return;
      }
      return onPrevRow?.();
    }, [onPrevRow, expandedRowState]);

    const nextRow = useCallback(async () => {
      if (expandedRowState !== "fully_loaded") {
        return;
      }
      return onNextRow?.();
    }, [onNextRow, expandedRowState]);

    const [isAutoAdvanceEnabled, setAutoAdvanceEnabled] = useEntityStorage({
      entityType: "project",
      entityIdentifier: params?.project ?? "",
      key: "humanReviewAutoAdvance",
    });

    const { enableScope, disableScope } = useHotkeysContext();

    useEffect(() => {
      if (rest.open) {
        enableScope(HotkeyScope.HumanReview);
        disableScope("sidepanel");
      } else {
        disableScope(HotkeyScope.HumanReview);
        enableScope("sidepanel");
      }
      return () => {
        disableScope(HotkeyScope.HumanReview);
        enableScope("sidepanel");
      };
    }, [enableScope, disableScope, rest.open]);

    useHotkeys(
      ["left", "k"],
      (e) => {
        e.stopPropagation();
        prevRow();
      },
      {
        description: "Previous row",
        scopes: ["human-review"],
        preventDefault: true,
      },
    );
    useHotkeys(
      ["right", "j"],
      (e) => {
        e.stopPropagation();
        nextRow();
      },
      {
        description: "Next row",
        scopes: ["human-review"],
        preventDefault: true,
      },
    );

    const configureScoresHref = `/app/${params?.org}/p/${params?.project}/configuration/review`;

    const minMainPanelWidth = usePanelSize(400);
    const minSidePanelWidth = usePanelSize(400);

    const [typedRowIndex, setTypedRowIndex] = useState<string | undefined>();

    // If the row index changes via the next/prev buttons, and the user has typed something
    // into the row index input, clear the input and accept the next/prev row change
    const [prevRowIndex, setPrevRowIndex] = useState<number | undefined>(
      rowIndex,
    );
    if (prevRowIndex !== rowIndex) {
      setPrevRowIndex(rowIndex);
      setTypedRowIndex(undefined);
    }

    const showRowInput =
      rowIndex != undefined &&
      rowIndex > 0 &&
      totalRows != undefined &&
      totalRows > 0;

    return (
      <Dialog {...rest}>
        <DialogTitle />
        <DialogDescription />
        <DialogContent
          aria-describedby={undefined}
          onEscapeKeyDown={(e) => {
            e.preventDefault();
            e.stopPropagation();
            if (isKeyboardEventTriggeredByInput(e)) return;
            setHumanReviewState(null);
          }}
          onOpenAutoFocus={(e) => e.preventDefault()}
          hideCloseButton
          className="flex h-screen max-h-screen w-screen flex-col gap-0 overflow-hidden rounded-none border-none p-0 outline-none sm:max-w-none"
        >
          <div className="flex h-18 flex-none items-center gap-2 border-b px-5">
            <div className="flex grow flex-col gap-1">
              <span className="grow truncate font-semibold">
                Human review{" "}
                <span className="ml-2 font-mono text-xs font-medium text-primary-500">
                  {rowKey}
                </span>
              </span>
              <DefaultBreadcrumbSection className="text-xs text-primary-500" />
            </div>
            <div className="flex flex-none items-center gap-2">
              {savingState && (
                <SavingStatus
                  className="mr-3 text-xs"
                  iconClassName="!size-3"
                  state={savingState}
                />
              )}
              {showRowInput && (
                <form
                  onSubmit={(e) => {
                    e.preventDefault();

                    const formData = new FormData(e.currentTarget);
                    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                    const submitted = formData.get("typedRowIndex") as string;

                    onJumpToRow?.(Number(submitted));
                  }}
                >
                  <div className="flex items-center">
                    <span className="mr-2 text-xs text-primary-500">Row</span>
                    <Input
                      type="number"
                      min={1}
                      max={totalRows}
                      name="typedRowIndex"
                      autoComplete="off"
                      placeholder={`${rowIndex}`}
                      className="h-5 w-9 rounded px-1 text-center text-xs !bg-transparent text-primary-500 hover:text-primary-700 focus:text-primary-700"
                      value={typedRowIndex ?? rowIndex}
                      onChange={(e) => {
                        setTypedRowIndex(e.target.value);
                      }}
                    />
                    <span className="mx-2 text-xs text-primary-500">
                      {`of ${totalRows}`}
                    </span>
                  </div>
                </form>
              )}
              <Button
                size="xs"
                className="outline-none"
                disabled={!onPrevRow || expandedRowState !== "fully_loaded"}
                onClick={() => prevRow()}
              >
                <span className="hidden md:block">Previous row</span>
                <span className="text-primary-400">←</span>
              </Button>
              <Button
                size="xs"
                className="outline-none"
                disabled={!onNextRow || expandedRowState !== "fully_loaded"}
                onClick={() => nextRow()}
              >
                <span className="hidden md:block">Next row</span>
                <span className="text-primary-400">→</span>
              </Button>
              <DialogClose asChild>
                <Button size="xs" Icon={X} />
              </DialogClose>
            </div>
          </div>
          {scoresArray.length === 0 && expandedRowState === "fully_loaded" ? (
            <div className="p-4">
              <TableEmptyState
                Icon={Settings2}
                label={
                  isDatasetRow
                    ? "There are no human review scores with the 'Write to expected' option configured for this project"
                    : "There are no human review scores configured for this project"
                }
              >
                <Link
                  className="text-sm font-medium text-accent-600"
                  href={configureScoresHref}
                >
                  Configure scores
                </Link>
              </TableEmptyState>
            </div>
          ) : (
            <div className="flex flex-1 overflow-hidden">
              <ResizablePanelGroup
                direction="horizontal"
                autoSaveId="expandedHumanReviewModalLayout"
                className="flex flex-1"
              >
                <ResizablePanel
                  order={1}
                  defaultSize={minMainPanelWidth}
                  minSize={minMainPanelWidth}
                  className="flex"
                  id="main"
                >
                  <div className="flex flex-1 flex-col overflow-auto px-5 py-4">
                    <div className="mb-2 flex h-7 items-center gap-2 text-xs">
                      Scores
                      <div className="flex grow items-center justify-end gap-2">
                        <Link
                          className="text-xs font-medium text-accent-600"
                          href={configureScoresHref}
                        >
                          Configure scores
                        </Link>
                      </div>
                    </div>
                    {rest.open && (
                      <ExpandedHumanReviewForm
                        scores={scoresArray}
                        isAutoAdvanceEnabled={isAutoAdvanceEnabled}
                        expandedRowState={expandedRowState}
                        isLoading={false}
                        updateScores={updateScores}
                        rowKey={rowKey}
                        xactId={xactId}
                        onAutoAdvanceToNextRow={nextRow}
                      />
                    )}
                    <div className="grow" />
                    <div className="mt-12 text-xs text-primary-500">
                      <div className="flex flex-col items-start gap-1 py-2">
                        <a
                          className={cn(
                            buttonVariants({ size: "xs" }),
                            "text-primary-600",
                          )}
                          onClick={() =>
                            setAutoAdvanceEnabled(!isAutoAdvanceEnabled)
                          }
                        >
                          <Switch
                            className="scale-75"
                            checked={isAutoAdvanceEnabled}
                          />
                          Auto-advance
                        </a>
                        Move to next score/row when using keyboard to set score
                      </div>
                    </div>
                  </div>
                </ResizablePanel>
                <ResizableHandle />
                <ResizablePanel
                  order={2}
                  defaultSize={minSidePanelWidth}
                  minSize={minSidePanelWidth}
                  className="flex"
                  id="details"
                >
                  <div className="flex flex-1 flex-col gap-3 overflow-auto px-5 pt-4 @container">
                    <div className="mb-3 flex items-center justify-between text-xs">
                      Span details
                      <div className="flex gap-2">
                        {currentTraceViewers && (
                          <RosterAvatars
                            roster={currentTraceViewers}
                            context="row"
                          />
                        )}
                        {actions}
                      </div>
                    </div>
                    {children}
                  </div>
                </ResizablePanel>
              </ResizablePanelGroup>
            </div>
          )}
        </DialogContent>
      </Dialog>
    );
  },
);

ExpandedHumanReviewModal.displayName = "ExpandedHumanReviewModal";
