"use server";

import { z } from "zod";

import {
  type PaginationParams,
  makeGetObjectsQuery,
} from "#/pages/api/_object_crud_util";
import type { AuthLookup } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  datasetSchema,
  experimentSchema,
  projectSchema,
  userSchema,
} from "@braintrust/typespecs";
import { otelWrapTraced } from "#/utils/tracing";
import { DEFAULT_PAGINATION_PARAMS } from "#/utils/api/pagination";

const projectContextProjectSchema = z.object({
  id: projectSchema.shape.id,
  settings: projectSchema.shape.settings,
});
export type ProjectContextProject = z.infer<typeof projectContextProjectSchema>;

const projectContextExperimentSchema = z.object({
  id: experimentSchema.shape.id,
  name: experimentSchema.shape.name,
  created: experimentSchema.shape.created,
  project_id: experimentSchema.shape.project_id,
  user: userSchema.nullable(),
  repo_info: experimentSchema.shape.repo_info
    .unwrap()
    .unwrap()
    .omit({ git_diff: true })
    .nullish(),
  metadata: experimentSchema.shape.metadata,
  description: experimentSchema.shape.description,
  dataset: z.string().nullish(),
  tags: experimentSchema.shape.tags,
});
export type ProjectContextExperiment = z.infer<
  typeof projectContextExperimentSchema
>;

const projectContextDatasetSchema = z.object({
  id: datasetSchema.shape.id,
  name: datasetSchema.shape.name,
  description: datasetSchema.shape.description,
  metadata: datasetSchema.shape.metadata,
  created: datasetSchema.shape.created,
  project_id: datasetSchema.shape.project_id,
  project_name: projectSchema.shape.name,
});
export type ProjectContextDataset = z.infer<typeof projectContextDatasetSchema>;

const projectIdOutputSchema = z.object({
  id: projectSchema.shape.id,
});

export type GetProjectContextInput = {
  org_name: string;
  project_name: string;
};

export const getProjectContextProjects = otelWrapTraced(
  "getProjectContextProjects",
  async (
    { org_name, project_name }: GetProjectContextInput,
    authLookupRaw?: AuthLookup,
  ): Promise<ProjectContextProject[]> => {
    const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

    const { query: allProjectsQuery, queryParams } = makeGetObjectsQuery({
      authLookup,
      permissionInfo: {
        aclObjectType: "project",
        aclPermission: "read",
      },
      filters: {
        org_name,
        name: project_name,
      },
      fullResultsSize: undefined,
      ...DEFAULT_PAGINATION_PARAMS,
    });

    const fullQuery = `
  select
    id, settings
  from (${allProjectsQuery}) "projects"
  `;

    const supabase = getServiceRoleSupabase();
    const { rows } = await supabase.query(fullQuery, queryParams.params);
    return projectContextProjectSchema.array().parse(rows ?? []);
  },
);

export const getProjectContextExperiments = otelWrapTraced(
  "getProjectContextExperiments",
  async (
    { org_name, project_name }: GetProjectContextInput,
    authLookupRaw?: AuthLookup,
  ): Promise<ProjectContextExperiment[]> => {
    const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

    const { query: allExperimentsQuery, queryParams } = makeGetObjectsQuery({
      authLookup,
      permissionInfo: {
        aclObjectType: "experiment",
        aclPermission: "read",
      },
      filters: {
        org_name,
        project_name,
      },
      fullResultsSize: undefined,
      ...DEFAULT_PAGINATION_PARAMS,
    });

    const fullQuery = `
  select
    experiments.id,
    experiments.name,
    experiments.created,
    experiments.project_id,
    (to_jsonb(users) - '{authLookup}'::text[]) user,
    -- Do not send git diffs to the client, as they can be huge and quickly
    -- surpass the 4.5MB response limit.
    (experiments.repo_info - '{git_diff}'::text[]) repo_info,
    experiments.metadata,
    experiments.description,
    -- RBAC_DISCLAIMER: It is okay to return the dataset_name if the caller can discover the dataset_id
    datasets.name as dataset,
    experiments.tags AS tags
  from
    (${allExperimentsQuery}) "experiments"
    LEFT JOIN users on experiments.user_id = users.id
    LEFT JOIN datasets on experiments.dataset_id = datasets.id
  `;

    const supabase = getServiceRoleSupabase();
    const { rows } = await supabase.query(fullQuery, queryParams.params);
    return projectContextExperimentSchema.array().parse(rows ?? []);
  },
);

export const getProjectContextDatasets = otelWrapTraced(
  "getProjectContextDatasets",
  async (
    {
      org_name,
      paginationParams,
    }: Pick<GetProjectContextInput, "org_name"> & PaginationParams,
    authLookupRaw?: AuthLookup,
  ): Promise<ProjectContextDataset[]> => {
    const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

    const { query: orgDatasetsQuery, queryParams } = makeGetObjectsQuery({
      authLookup,
      permissionInfo: {
        aclObjectType: "dataset",
        aclPermission: "read",
      },
      fullResultSetAdditionalProjections: ["projects.name as project_name"],
      filters: {
        org_name,
      },
      fullResultsSize: undefined,
      ...(paginationParams
        ? {
            ...DEFAULT_PAGINATION_PARAMS,
            paginationParams: {
              ...DEFAULT_PAGINATION_PARAMS.paginationParams,
              ...paginationParams,
            },
          }
        : DEFAULT_PAGINATION_PARAMS),
    });

    const fullQuery = `
  select
    datasets.id,
    datasets.name,
    datasets.description,
    datasets.metadata,
    datasets.created,
    datasets.project_id,
    datasets.project_name
  from (${orgDatasetsQuery}) "datasets"
  `;

    const supabase = getServiceRoleSupabase();
    const { rows } = await supabase.query(fullQuery, queryParams.params);
    return projectContextDatasetSchema.array().parse(rows ?? []);
  },
);

export const getProjectDatasets = otelWrapTraced(
  "getProjectDatasets",
  async (
    { org_name, project_name }: GetProjectContextInput,
    authLookupRaw?: AuthLookup,
  ): Promise<ProjectContextDataset[]> => {
    const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

    const { query: projectDatasetsQuery, queryParams } = makeGetObjectsQuery({
      authLookup,
      permissionInfo: {
        aclObjectType: "dataset",
        aclPermission: "read",
      },
      fullResultSetAdditionalProjections: ["projects.name as project_name"],
      filters: {
        org_name,
        project_name,
      },
      fullResultsSize: undefined,
      ...DEFAULT_PAGINATION_PARAMS,
    });

    const fullQuery = `
  select
    datasets.id,
    datasets.name,
    datasets.description,
    datasets.metadata,
    datasets.created,
    datasets.project_id,
    datasets.project_name
  from (${projectDatasetsQuery}) "datasets"
  `;

    const supabase = getServiceRoleSupabase();
    const { rows } = await supabase.query(fullQuery, queryParams.params);
    return projectContextDatasetSchema.array().parse(rows ?? []);
  },
);

export const getProjectId = otelWrapTraced(
  "getProjectId",
  async (
    { org_name, project_name }: GetProjectContextInput,
    authLookupRaw?: AuthLookup,
  ): Promise<string | null> => {
    const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());

    const { query: allProjectsQuery, queryParams } = makeGetObjectsQuery({
      authLookup,
      permissionInfo: {
        aclObjectType: "project",
        aclPermission: "read",
      },
      filters: {
        org_name,
        name: project_name,
      },
      fullResultsSize: undefined,
      ...DEFAULT_PAGINATION_PARAMS,
    });

    const fullQuery = `select id from (${allProjectsQuery}) "projects"`;

    const supabase = getServiceRoleSupabase();
    const { rows } = await supabase.query(fullQuery, queryParams.params);
    const parsed = projectIdOutputSchema.array().parse(rows ?? []);
    if (parsed.length === 1) {
      return parsed[0].id;
    } else {
      return null;
    }
  },
);
