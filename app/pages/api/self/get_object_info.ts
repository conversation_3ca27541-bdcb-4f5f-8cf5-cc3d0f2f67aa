import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import { type AuthLookup } from "../_lookup_api_key";
import { SqlQueryParams } from "#/utils/sql-query-params";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { endpointSchemas } from "@braintrust/local/app-schema";
import { doubleQuote } from "#/utils/sql-utils";
import {
  aclCheckConditions,
  resolvePossiblyVirtualTableInfo,
  fromTablesClause,
  isActiveChecks,
} from "../_object_crud_util";
import { isEmpty, mapAt } from "braintrust/util";
import { type AclObjectType } from "@braintrust/typespecs";
import { aclSpecs } from "@braintrust/local/app-schema";
import { z } from "zod";
import { otelTraced, otelWrapTraced } from "#/utils/tracing";
import { isAllowedSysadmin } from "#/utils/derive-error-context";
//import { substituteParamsDebug } from "#/utils/sql-query-params";

const { input: paramsSchema, output: outputSchema } =
  endpointSchemas.self_get_object_info;

const legacyBackendAclObjectTypes = new Set<AclObjectType>([
  "organization",
  "project",
  "experiment",
  "dataset",
  "prompt",
  "prompt_session",
  "group",
  "role",
]);

export const helper = otelWrapTraced(
  "get_object_info_helper",
  async (params: z.infer<typeof paramsSchema>, authLookup: AuthLookup) => {
    const {
      object_type,
      override_restrict_object_type,
      object_ids,
      accept_arbitrary_acl_object_types,
      allow_sysadmin_roles,
      include_deleted_objects,
    } = params;

    const shouldReturnObjectType = (x: AclObjectType) =>
      accept_arbitrary_acl_object_types || legacyBackendAclObjectTypes.has(x);
    const isAllowedSysadminRes = await (async () => {
      if (
        !(allow_sysadmin_roles && allow_sysadmin_roles.includes("sysadmin"))
      ) {
        return false;
      }
      return await isAllowedSysadmin(authLookup);
    })();

    const { parentAclObjectTypes } = aclSpecs[object_type];
    const allAclObjectTypes = [object_type, ...parentAclObjectTypes];
    const baseObjectTableInfo = resolvePossiblyVirtualTableInfo(object_type);
    const objectTypeToColNames = new Map<
      AclObjectType,
      { idCol: string; nameCol: string }
    >(
      allAclObjectTypes.map((objectType) => [
        objectType,
        { idCol: `${objectType}_id`, nameCol: `${objectType}_name` },
      ]),
    );
    const colProjections = [];
    const colAliases = [];
    for (const [objectType, projectionCols] of objectTypeToColNames.entries()) {
      const tableInfo = resolvePossiblyVirtualTableInfo(objectType);
      colProjections.push(
        `${doubleQuote(tableInfo.tableName)}.id as ${projectionCols.idCol}`,
      );
      colProjections.push(
        `${doubleQuote(tableInfo.tableName)}.${doubleQuote(
          tableInfo.nameCol,
        )} as ${projectionCols.nameCol}`,
      );
      colAliases.push(projectionCols.idCol, projectionCols.nameCol);
    }

    const queryParams = new SqlQueryParams();
    // RBAC_DISCLAIMER: We allow access to the id and object name info for an
    // object if the querier is part of the object's organization or they have
    // access through an ACL, or they are a sysadmin.
    const query = `
    with
    full_table as (
        select distinct
            ${colProjections.join(", ")},
            _expanded_acls.permission permission,
            not (${isActiveChecks(allAclObjectTypes)}) as is_deleted
        from
            ${fromTablesClause(allAclObjectTypes)}
            left join _expanded_acls on (${aclCheckConditions({
              queryParams,
              user_id: authLookup.user_id,
              allowAnonAccess: true,
              baseObjectType: object_type,
              parentObjectTypes: parentAclObjectTypes,
              overrideRestrictObjectType:
                override_restrict_object_type ?? undefined,
            })})
        where
            ${doubleQuote(baseObjectTableInfo.tableName)}.id in (${object_ids
              .map((object_id) => queryParams.add(object_id))
              .concat(["null"])
              .map((x) => `uuid_or_null(${x})`)
              .join(", ")})
            ${
              include_deleted_objects
                ? ""
                : `and ${isActiveChecks(allAclObjectTypes)}`
            }
    ),
    grouped_permissions as (
        select ${colAliases}, coalesce(array_agg(permission::text) filter (where permission is not null), '{}') permissions, bool_or(is_deleted) as is_deleted
        from full_table
        group by ${colAliases}
    )
    select * from grouped_permissions
    where cardinality(permissions) > 0 or (
        true
        ${
          allAclObjectTypes.includes("organization")
            ? `and (${object_type === "role" ? "organization_id is null" : "false"}) or exists (
                select 1 from members
                where
                    members.org_id = organization_id
                    and members.user_id = ${queryParams.add(authLookup.user_id)}
               )`
            : ""
        }
    ) ${isAllowedSysadminRes ? "or true" : ""}
  `;

    //console.log("Running query\n", substituteParamsDebug(query, queryParams.params));
    const supabase = getServiceRoleSupabase();
    const { rows } = await otelTraced("get_object_info_query", () =>
      supabase.query(query, queryParams.params),
    );
    // Post-process each row to match the output schema.
    return rows.map((r) => {
      const baseColNames = mapAt(objectTypeToColNames, object_type);
      const object_id = r[baseColNames.idCol];
      const object_name = r[baseColNames.nameCol];
      const parent_cols = Object.fromEntries(
        parentAclObjectTypes
          .filter(shouldReturnObjectType)
          .map((objectType) => {
            const colNames = mapAt(objectTypeToColNames, objectType);
            const parsedColNames = z
              .object({
                id: z.string().nullish(),
                name: z.string().nullish(),
              })
              .parse({ id: r[colNames.idCol], name: r[colNames.nameCol] });
            const out: [
              objectType: AclObjectType,
              {
                id?: string | null | undefined;
                name?: string | null | undefined;
              },
            ] = [objectType, parsedColNames];
            return out;
          })
          .filter((x) => !isEmpty(x[1].id) && !isEmpty(x[1].name)),
      );
      const permissions = r.permissions;
      const is_deleted = r.is_deleted;
      return {
        object_id,
        object_name,
        parent_cols,
        permissions,
        is_deleted,
        // Important: for old data planes which strict-parse the response
        // schema, we cannot include the 'is_allowed_sysadmin' field.
        ...(isAllowedSysadminRes ? { is_allowed_sysadmin: true } : {}),
      };
    });
  },
);

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(req, res, helper, { paramsSchema, outputSchema });
}
