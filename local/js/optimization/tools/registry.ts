import { type Too<PERSON>, type <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./types";

import { zodToJsonSchema } from "zod-to-json-schema";
import { EditTaskTool } from "./edit-task";
import { EditDataTool } from "./edit-data";
import { ChatCompletionTool } from "openai/resources";
import { GetSummaryTool, GetResultsTool, RerunTaskTool } from "./get-results";
import { ContinueExecutionTool } from "./continue-execution";
import { Span } from "braintrust";
import { z } from "zod";
import { GetAvailableScorersTool } from "./get-scorers";
import { EditScorersTool } from "./edit-scorers";
import { BTQLQueryTool, InferSchemaTool } from "./query";
import { CreateCodeScorerTool, CreateLLMScorerTool } from "./create-scorer";
import { DocsTool } from "./docs";

export const ToolRegistry = {
  edit_task: EditTaskTool,
  get_summary: GetSummaryTool,
  get_results: GetResultsTool,
  run_task: RerunTaskTool,
  edit_data: EditDataTool,
  get_available_scorers: GetAvailableScorersTool,
  edit_scorers: EditScorersTool,
  create_code_scorer: CreateCodeScorerTool,
  infer_schema: InferSchemaTool,
  btql_query: BTQLQueryTool,
  create_llm_scorer: CreateLLMScorerTool,
  search_docs: DocsTool,

  // This is a special tool that allows the LLM to prompt the user to keep going after
  // it runs out of MAX_ROUNDTRIPS.
  continue_execution: ContinueExecutionTool,
} as const;

export type ToolName = keyof typeof ToolRegistry;
// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
export const ALL_TOOL_NAMES = Object.keys(ToolRegistry) as ToolName[];

export function validToolName(name: string): ToolName {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  if (ALL_TOOL_NAMES.includes(name as ToolName)) {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return name as ToolName;
  }
  throw new Error(`Unknown tool name: ${name}`);
}
export function isValidToolName(name: string): name is ToolName {
  try {
    validToolName(name);
    return true;
  } catch {
    return false;
  }
}

type ToolHandlers = {
  [K in keyof typeof ToolRegistry]: ToolHandler<(typeof ToolRegistry)[K]>;
};

interface ToolImplementation<T extends Tool<unknown, unknown>> {
  schema: T;
  handler: ToolHandler<T>;
}

export class ToolManager {
  private implementations: Partial<
    Record<ToolName, ToolImplementation<Tool<unknown, unknown>>>
  > = {};
  private cachedTools: Map<ToolName, ChatCompletionTool> = new Map();

  constructor(private readonly isGoogle: boolean) {}

  updateImplementations(implementations: Partial<ToolHandlers>) {
    Object.assign(
      this.implementations,
      Object.fromEntries(
        Object.entries(implementations).map(([name, handler]) => [
          name,
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          { schema: ToolRegistry[name as ToolName], handler },
        ]),
      ),
    );
  }

  getOpenAITool(name: ToolName): ChatCompletionTool {
    const cached = this.cachedTools.get(name);
    if (cached) {
      return cached;
    }

    const tool = ToolRegistry[name];

    const openaiTool: ChatCompletionTool = {
      type: "function",
      function: {
        name: name,
        description: tool.description,
        parameters:
          this.isGoogle && name === "edit_task"
            ? googleEditSchema
            : convertToJsonSchema({
                schema: tool.parameters,
                isGoogle: this.isGoogle,
              }),
      },
    };
    this.cachedTools.set(name, openaiTool);
    return openaiTool;
  }

  async executeTool(
    name: ToolName,
    args: unknown,
    span: Span,
    abortController?: AbortController,
  ) {
    const implementation = this.implementations[name];
    if (!implementation) {
      throw new Error(`Tool ${name} not initialized.`);
    }
    const parsedArgs = implementation.schema.parameters.parse(args);
    return implementation.handler(parsedArgs, span, abortController);
  }

  get tools(): ToolHandlers {
    const ret: Record<string, ToolHandler<Tool<unknown, unknown>>> = {};
    for (const name of ALL_TOOL_NAMES) {
      if (!this.implementations[name]) {
        throw new Error(`Tool ${name} not initialized.`);
      }
      ret[name] = this.implementations[name].handler;
    }
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return ret as ToolHandlers;
  }

  list(): ToolName[] {
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return Object.entries(this.implementations)
      .filter(([_, impl]) => !!impl.handler)
      .map(([name]) => name) as ToolName[];
  }
}

function convertToJsonSchema({
  schema,
  isGoogle,
}: {
  schema: z.ZodSchema;
  isGoogle: boolean;
}) {
  const jsonSchema = zodToJsonSchema(schema);
  if (isGoogle) {
    delete jsonSchema.$schema;
  }
  return jsonSchema;
}

const googleEditSchema = {
  additionalProperties: false,
  properties: {
    edits: {
      items: {
        additionalProperties: false,
        properties: {
          diff: {
            description:
              "The diff to apply to the value. Use standard `diff` format, like what would be generated by `git diff`. The diff will be applied with the `applyPatch` function from the `diff` package.",
            type: "string",
          },
          path: {
            description:
              "The path to the value to edit (relative to definition). Use strings for keys and numbers (0-indexed) for array indices. The path must point to a value (e.g. a string), not an array or object.",
            items: {
              type: "string",
            },
            type: "array",
          },
        },
        required: ["path", "diff"],
        type: "object",
      },
      type: "array",
    },
    index: {
      description: "The index of the task to edit",
      type: "number",
    },
  },
  required: ["index", "edits"],
  type: "object",
};
