"use client";

import { cn } from "#/utils/classnames";
import { type RowData, type Row } from "@tanstack/react-table";
import { Star } from "lucide-react";
import { useCallback, useState } from "react";
import { flattenDiffObjects } from "#/utils/diffs/diff-objects";
import { z } from "zod";
import { useOptimisticState } from "#/utils/optimistic-update";
import { type TransactionId } from "braintrust/util";
import { type UpdateRowFn } from "#/utils/mutable-object";

export const STARRED_TAG = "__starred";

const tagsSchema = z.array(z.string());

const parseTags = (tags: string) => {
  const rowTags = tagsSchema.safeParse(
    Array.isArray(tags) ? tags : JSON.parse(tags ?? "[]"),
  );
  return rowTags.success ? rowTags.data : [];
};

export const StarCell = <T extends RowData>({
  row,
  updateRow,
}: {
  row: Row<T>;
  updateRow: UpdateRowFn;
}) => {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
  const originalRow = flattenDiffObjects(row.original) as {
    tags: string;
    id: string;
    _xact_id: string;
  };

  const [isStarred, setIsStarred] = useState(
    parseTags(originalRow.tags).includes(STARRED_TAG),
  );

  const updateStarred = useCallback(
    async (newIsStarred: boolean) => {
      setIsStarred(newIsStarred);
      const parsedTags = parseTags(originalRow.tags);
      const newTags = newIsStarred
        ? parsedTags.concat(STARRED_TAG)
        : parsedTags.filter((t) => t !== STARRED_TAG);

      const xactId = await updateRow(originalRow, ["tags"], newTags);
      return xactId;
    },
    [originalRow, updateRow],
  );

  const { save: toggleStar } = useOptimisticState({
    xactId: originalRow._xact_id,
    value: parseTags(originalRow.tags).includes(STARRED_TAG),
    save: async (): Promise<TransactionId | null> => {
      return await updateStarred(!isStarred);
    },
    rowKey: originalRow.id,
  });

  return (
    <div
      onClick={toggleStar}
      className={cn(
        "flex size-full cursor-pointer pt-2.5 justify-center text-primary-300",
        {
          "text-primary-500": isStarred,
          "opacity-0 group-hover/tablerow:opacity-100 hover:text-primary-400":
            !isStarred,
        },
      )}
    >
      <Star
        className={cn("size-4", {
          "fill-primary-500": isStarred,
        })}
      />
    </div>
  );
};
