{"name": "braintrustdata", "version": "0.1.0", "private": true, "experiments": {"asyncWebAssembly": true}, "scripts": {"dev": "next dev --turbo", "dev:email": "email dev --port 3333 --dir ./ui/email-templates", "build": "ESLINT_FULL=1 npx eslint . --max-warnings=0 && NODE_OPTIONS=\"--max-old-space-size=8192\" next build", "typecheck": "tsc --noEmit --watch", "start": "next start", "lint": "next lint", "supabase": "supabase", "test": "npx vitest run", "analyze-build": "NODE_OPTIONS=\"--max-old-space-size=8192\" ANALYZE=true next build", "test-playwright": "npx playwright test -c tests/playwright/playwright.config.ts", "test-momentic": "OPENAI_API_KEY=$OPENAI_API_KEY npx momentic@latest run tests/momentic --parallel 5", "start-momentic": "OPENAI_API_KEY=$OPENAI_API_KEY npx momentic@latest app", "build:mustache": "cd utils/mustache && lezer-generator mustache.grammar -o parser.js", "postinstall": "fumadocs-mdx"}, "dependencies": {"@apidevtools/json-schema-ref-parser": "^11.6.4", "@braintrust/btql": "workspace:*", "@braintrust/local": "workspace:*", "@braintrust/openapi": "workspace:*", "@braintrust/proxy": "workspace:*", "@braintrust/realtime": "workspace:*", "@braintrust/typespecs": "workspace:*", "@calcom/embed-react": "^1.5.1", "@clerk/nextjs": "6.23.3", "@clerk/types": "^4.62.0", "@codemirror/autocomplete": "^6.11.1", "@codemirror/commands": "^6.6.0", "@codemirror/lang-javascript": "^6.2.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-python": "^6.1.3", "@codemirror/lang-sql": "^6.8.0", "@codemirror/lang-yaml": "^6.1.2", "@codemirror/language": "^6.10.1", "@codemirror/lint": "^6.4.2", "@codemirror/merge": "^6.7.3", "@codemirror/search": "^6.5.6", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.34.3", "@codesandbox/sandpack-react": "^2.19.10", "@datadog/browser-rum": "^6.16.0", "@date-fns/utc": "^2.1.1", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@duckdb/duckdb-wasm": "1.29.1-dev132.0", "@emotion/react": "^11.14.0", "@flags-sdk/hypertune": "^0.2.0", "@hookform/resolvers": "^3.3.4", "@lezer/common": "^1.2.1", "@lezer/generator": "^1.6.0", "@lezer/highlight": "^1.2.0", "@lezer/lr": "^1.4.0", "@noble/ed25519": "^2.2.2", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.53.0", "@opentelemetry/instrumentation": "^0.46.0", "@opentelemetry/sdk-logs": "^0.53.0", "@playwright/test": "^1.44.1", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dismissable-layer": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menu": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.2.0", "@radix-ui/react-visually-hidden": "^1.1.0", "@react-aria/focus": "^3.20.2", "@react-email/components": "^0.0.41", "@react-email/tailwind": "^1.2.2", "@react-spring/web": "^9.7.3", "@replit/codemirror-indentation-markers": "^6.5.0", "@scalar/api-client-react": "^1.2.7", "@segment/analytics-node": "^2.1.2", "@segment/snippet": "^4.16.2", "@sentry/nextjs": "^8.49.0", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.6.0", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-query": "^5.45.1", "@tanstack/react-table": "^8.17.0", "@tanstack/react-virtual": "^3.13.12", "@typescript/vfs": "^1.6.0", "@uiw/codemirror-extensions-hyper-link": "^4.23.10", "@uiw/codemirror-theme-github": "^4.23.10", "@uiw/react-codemirror": "^4.23.10", "@upstash/ratelimit": "^0.4.3", "@valtown/codemirror-ts": "^2.2.1", "@vercel/blob": "^0.16.1", "@vercel/edge-config": "^1.4.0", "@vercel/functions": "^1.0.2", "@vercel/kv": "^0.2.2", "@vercel/otel": "^1.10.0", "@xyflow/react": "^12.4.4", "ai": "^2.2.30", "ajv": "^8.12.0", "apache-arrow": "^17.0.0", "async-mutex": "^0.4.0", "autoevals": "workspace:*", "aws-jwt-verify": "^4.0.0", "braintrust": "workspace:*", "class-variance-authority": "^0.7.0", "clsx": "^1.2.1", "cmdk": "^1.1.1", "codemirror-copilot": "workspace:*", "content-security-policy-builder": "^2.3.0", "content-security-policy-parser": "^0.6.0", "d3": "^7.8.5", "d3-dsv": "^3.0.1", "date-fns": "^4.1.0", "diff": "^5.1.0", "eventsource-parser": "^1.1.1", "fast-glob": "^3.3.3", "flags": "^4.0.1", "fumadocs-core": "^14.7.7", "fumadocs-mdx": "11.2.0", "fumadocs-openapi": "^5.12.0", "fumadocs-ui": "^14.6.3", "fuse.js": "^7.0.0", "gray-matter": "^4.0.3", "hast-util-to-jsx-runtime": "^2.3.0", "humanize-duration": "^3.32.0", "hypertune": "^2.7.2", "ics": "^3.8.1", "immer": "^10.0.3", "is-dom": "^1.1.0", "jotai": "^2.12.2", "jotai-effect": "^2.0.1", "jotai-optics": "^0.4.0", "jotai-scope": "^0.7.3", "jsonwebtoken": "^9.0.2", "lexorank": "^1.0.5", "lodash": "^4.17.21", "lodash.isequal": "^4.5.0", "lottie-react": "^2.4.1", "lru-cache": "^10.1.0", "lucide-react": "^0.540.0", "mime-types": "^3.0.1", "motion": "^12.6.5", "mustache": "^4.2.0", "next": "^15.4.1", "next-auth": "5.0.0-beta.25", "next-themes": "^0.4.4", "node-html-parser": "^6.1.12", "nuqs": "^2.3.1", "optics-ts": "^2.4.1", "orb-billing": "^4.71.2", "path-to-regexp": "^8.2.0", "pg": "^8.11.3", "pluralize": "^8.0.0", "pretty-bytes": "^6.1.1", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-awesome-reveal": "^4.2.14", "react-day-picker": "^8.9.1", "react-dom": "^19.0.0", "react-dropzone": "^14.2.3", "react-email": "^4.2.6", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.62.0", "react-hotkeys-hook": "^4.5.0", "react-image-crop": "^11.0.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.6", "react-textarea-autosize": "^8.5.3", "react-twitter-embed": "^4.0.4", "react-use-event-hook": "^0.9.6", "react-youtube": "^10.1.0", "rehype-external-links": "^3.0.0", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-mdx": "^3.1.0", "remark-parse": "^11.0.0", "remark-stringify": "^11.0.0", "resend": "^4.5.1", "scroll-into-view-if-needed": "^3.1.0", "semver": "^7.6.0", "server-only": "^0.0.1", "sharp": "^0.33.3", "shiki": "^3.1.0", "slugify": "^1.6.6", "sonner": "^1.5.0", "stripe": "^17.6.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.14", "tailwindcss-animate": "^1.0.7", "throttle-debounce": "^5.0.2", "typescript": "^5.5.4", "unified": "^11.0.0", "unist-util-visit": "^5.0.0", "untruncate-json": "^0.0.1", "use-memo-one": "^1.1.3", "uuid": "^9.0.1", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.5"}, "devDependencies": {"@limegrass/eslint-plugin-import-alias": "^1.4.1", "@napi-rs/simple-git": "^0.1.9", "@next/bundle-analyzer": "15.0.0", "@react-email/preview-server": "4.2.6", "@simbathesailor/use-what-changed": "^2.0.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.13", "@tanstack/react-query-devtools": "^5.64.2", "@types/ajv": "^1.0.0", "@types/d3": "^7.4.3", "@types/d3-dsv": "^3.0.7", "@types/diff": "^5.0.9", "@types/humanize-duration": "^3.27.4", "@types/is-dom": "^1.1.2", "@types/jsonwebtoken": "^9.0.5", "@types/lodash.isequal": "^4.5.8", "@types/mdx": "^2.0.13", "@types/mime-types": "^2.1.4", "@types/mustache": "^4.2.5", "@types/node": "^20", "@types/pg": "^8.11.14", "@types/pluralize": "^0.0.30", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@types/segment-analytics": "^0.0.34", "@types/semver": "^7.5.8", "@types/throttle-debounce": "^5.0.2", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^8.11.0", "@typescript-eslint/parser": "^8.11.0", "@vitejs/plugin-react": "^4.3.1", "api": "link:@types/opentelemetry/api", "autoprefixer": "^10.4.20", "duckdb": "^1.0.0", "eslint": "^8.57.1", "eslint-config-next": "15.1.2", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react-compiler": "19.0.0-beta-37ed2a7-20241206", "eslint-plugin-tailwindcss": "^3.17.5", "json-refs": "^3.0.15", "npm-check-updates": "^16.14.12", "postcss": "^8.4.44", "postcss-nesting": "^13.0.0", "prettier": "^3.4.1", "schema-dts": "^1.1.5", "supabase": "^1.148.6", "tailwindcss-oklch": "^0.0.1", "vercel": "^33.0.1", "vite-tsconfig-paths": "^4.3.1", "vitest": "^2.1.9"}}